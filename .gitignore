# ===========================================
# 多模块项目 .gitignore 配置
# ===========================================

# ===========================================
# 操作系统相关文件
# ===========================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.tmp
*.temp
Desktop.ini

# Linux
*~

# ===========================================
# IDE 和编辑器相关
# ===========================================
# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Eclipse
.metadata
.project
.classpath
.settings/
*.launch
.loadpath
.recommenders

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# ===========================================
# Java / Maven 项目 (xtand-server, approval-server, patellar-server)
# ===========================================
# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle (如果有使用)
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# Java
*.class
*.log
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*

# Spring Boot
spring.log

# ===========================================
# Node.js / Vue.js 项目 (xtand-admin-console)
# ===========================================
# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建输出
dist/
build/
.output/
.nuxt/
.next/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 包管理器锁文件 (根据团队约定选择保留或忽略)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Vite
.vite/
vite.config.js.timestamp-*

# 测试覆盖率
coverage/
*.lcov

# ===========================================
# 移动端开发 (PatellarApp)
# ===========================================
# Android
*.apk
*.ap_
*.aab
*.dex
local.properties
proguard/
*.keystore
!debug.keystore

# iOS
*.ipa
*.dSYM.zip
*.dSYM
DerivedData/
*.mobileprovision
*.p12

# React Native
# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
*.keystore
!debug.keystore

# Bundle artifacts
*.jsbundle

# CocoaPods
/ios/Pods/

# Expo
.expo/
web-build/

# ===========================================
# 日志文件
# ===========================================
logs/
*.log
log/

# ===========================================
# 数据库文件
# ===========================================
*.db
*.sqlite
*.sqlite3

# ===========================================
# 临时文件和缓存
# ===========================================
*.tmp
*.temp
*.cache
.cache/

# ===========================================
# 配置文件 (包含敏感信息的)
# ===========================================
# 数据库配置 (生产环境)
application-prod.yml
application-production.yml

# 私有配置
config/local.yml
config/private.yml

# ===========================================
# 文档和设计文件 (可选)
# ===========================================
# 如果不想提交设计文件，取消注释
# *.psd
# *.sketch
# *.fig

# ===========================================
# 压缩文件
# ===========================================
*.zip
*.tar
*.tar.gz
*.7z

# ===========================================
# 备份文件
# ===========================================
*.bak
*.backup
*.orig 