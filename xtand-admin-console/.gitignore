# Node
node_modules/

# Build output
dist/

# Local env files
.env*

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
.DS_Store

# Mac system files
.DS_Store

# Optional: OS generated files
Thumbs.db

# Local test/preview
*.local

# Vite cache
.vite/

# Coverage
coverage/

# Optional: lock files (if you want to ignore them)
# package-lock.json
yarn.lock
pnpm-lock.yaml 