{"name": "xtand-admin-console", "private": true, "version": "0.1.0", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "build:zip": "vite build --mode production && cd dist && zip -r ../dist.zip ."}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.9", "element-plus": "^2.6.3", "pinia": "^2.1.7", "vue": "^3.4.15", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "less": "^4.2.0", "prettier": "^3.0.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10"}}