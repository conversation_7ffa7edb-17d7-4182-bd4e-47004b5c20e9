1、所有的后台接口的地址都是/admin/api/{模块名}/xxx 这种
2、所有的接口都需要在请求头中添加token
3、接口返回数据格式
```
{
  "success": true,
  "data": {
    
  },
  "errorCode": null,
  "errorMessage": null
}
```
4、success为true表示成功，其他表示失败
5、分页接口返回数据格式
```
{
  "success": true,
  "data": {
    "pageNo": 1,
    "pageSize": 10,
    "total": 0,
    "list": []
  },
  "errorCode": null,
  "errorMessage": null
}
```
6、分页接口参数
```
pageNum=1&pageSize=10
```
7、新增接口参数
```
{
  "name": "角色名",
  "code": "角色编码",
  "sort": 1,
  "status": 1,
  "remark": "备注"
}
```
8、编辑接口参数
```

在request.js中已经做了结果判断,到调用的方法中不需要判断data,直接data里面的内容就可以了
