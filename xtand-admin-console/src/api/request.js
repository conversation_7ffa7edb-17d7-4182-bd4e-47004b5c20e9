import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '../router'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 10000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      //config.headers['Authorization'] = `Bearer ${token}`
      config.headers['Authorization'] = `${token}`
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    console.log('Response:', res) // 添加日志
    // 这里统一处理响应
    if (res.success === true) {
      return res.data
    } else {
      // 处理3040错误码（token失效）
      if (res.errorCode === '3040') { // 修改为字符串比较
        console.log('检测到3040错误') // 添加日志
        ElMessage.error(res.errorMessage || '登录已过期，请重新登录')
        // 清除本地存储的token和用户信息
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        // 重定向到登录页
        router.replace('/login')
        return Promise.reject(new Error(res.errorMessage || '登录已过期'))
      }
      ElMessage.error(res.errorMessage || '请求失败')
      return Promise.reject(new Error(res.errorMessage || '请求失败'))
    }
  },
  error => {
    console.error('响应错误:', error)
    // 处理后端返回的错误
    const errorResponse = error.response?.data
    if (errorResponse?.errorCode === '3040') { // 修改为字符串比较
      console.log('检测到3040错误（error handler）') // 添加日志
      ElMessage.error(errorResponse.errorMessage || '登录已过期，请重新登录')
      // 清除本地存储的token和用户信息
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      // 重定向到登录页
      router.replace('/login')
      return Promise.reject(new Error(errorResponse.errorMessage || '登录已过期'))
    }
    const errorMsg = errorResponse?.errorMessage || '网络错误'
    ElMessage.error(errorMsg)
    return Promise.reject(new Error(errorMsg))
  }
)

export default service