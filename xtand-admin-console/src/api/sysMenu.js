import request from '@/api/request'

// 当前用户菜单权限
export function fetchMyMenu() {
  return request({
    url: '/admin/api/menu/myMenu',
    method: 'get'
  })
}

// 菜单树/列表
export function fetchMenuList() {
  console.log("load menu list")
  return request({
    url: '/admin/api/menu/list',
    method: 'get'
  })
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/admin/api/menu/add',
    method: 'post',
    data
  })
}

// 编辑菜单
export function editMenu(data) {
  return request({
    url: '/admin/api/menu/edit',
    method: 'post',
    data
  })
}

// 删除菜单
export function deleteMenu(id) {
  return request({
    url: `/admin/api/menu/delete/${id}`,
    method: 'post'
  })
}

// 菜单详情
export function fetchMenuDetail(id) {
  return request({
    url: `/admin/api/menu/detail/${id}`,
    method: 'get'
  })
}

// 查询角色已分配菜单ID
export function fetchRoleMenuIds(roleId) {
  return request({
    url: `/admin/api/menu/roleMenuIds/${roleId}`,
    method: 'get'
  })
}

// 角色分配菜单
export function assignMenu(roleId, menuIdList) {
  return request({
    url: '/admin/api/menu/assignMenu',
    method: 'post',
    params: { roleId },
    data: menuIdList
  })
} 