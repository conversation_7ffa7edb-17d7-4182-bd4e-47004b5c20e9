import request from '@/api/request'

// 角色分页查询
export function fetchRoleList(params) {
  return request({
    url: '/admin/api/role/list',
    method: 'get',
    params
  })
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/admin/api/role/add',
    method: 'post',
    data
  })
}

// 编辑角色
export function editRole(data) {
  return request({
    url: '/admin/api/role/edit',
    method: 'post',
    data
  })
}

// 删除角色
export function deleteRole(id) {
  return request({
    url: `/admin/api/role/delete/${id}`,
    method: 'post'
  })
}

// 角色详情
export function fetchRoleDetail(id) {
  return request({
    url: `/admin/api/role/detail/${id}`,
    method: 'get'
  })
}

// 查询所有角色（下拉用）
export function fetchAllRoles() {
  return request({
    url: '/admin/api/role/all',
    method: 'get'
  })
} 