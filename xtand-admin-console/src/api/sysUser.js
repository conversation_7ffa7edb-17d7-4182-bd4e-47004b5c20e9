import request from './request'




export function login(data) {
  console.log(data)
  return request({
    url: '/admin/api/sysUser/login',
    method: 'post',
    data
  })
}

/**
 * 系统用户API
 */

// 用户分页查询
export function fetchSysUserList(params) {
  return request({
    url: '/admin/api/sysUser/list',
    method: 'get',
    params
  })
}

// 新增用户
export function addSysUser(data) {
  return request({
    url: '/admin/api/sysUser/add',
    method: 'post',
    data
  })
}

// 编辑用户
export function editSysUser(data) {
  return request({
    url: '/admin/api/sysUser/edit',
    method: 'post',
    data
  })
}

// 删除用户
export function deleteSysUser(userId) {
  return request({
    url: `/admin/api/sysUser/delete/${userId}`,
    method: 'post'
  })
}

// 重置用户密码
export function resetUserPassword(userId) {
  return request({
    url: `/admin/api/sysUser/resetUserPassword`,
    method: 'post',
    data: { userId }
  })
}

// 用户详情
export function getSysUserDetail(userId) {
  return request({
    url: `/admin/api/sysUser/detail/${userId}`,
    method: 'get'
  })
}

// 分配角色
export function assignRoles(data) {
  return request({
    url: '/admin/api/sysUser/assignRoles',
    method: 'post',
    data
  })
}

// 查询用户已分配角色
export function getUserRoles(userId) {
  return request({
    url: `/admin/api/sysUser/roles/${userId}`,
    method: 'get'
  })
} 