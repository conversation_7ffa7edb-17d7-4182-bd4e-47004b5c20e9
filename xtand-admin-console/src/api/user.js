import request from './request'

/**
 * 会员管理API
 */

// 会员分页查询
export function fetchUserList(params) {
  return request({
    url: '/admin/api/user/list',
    method: 'get',
    params
  })
}

// 会员状态更新（启用/禁用）
export function updateUserStatus(params) {
  return request({
    url: '/admin/api/user/updateStatus',
    method: 'post',
    params
  })
}

// 会员详情查询
export function fetchUserDetail(id) {
  return request({
    url: `/admin/api/user/detail/${id}`,
    method: 'get'
  })
}
