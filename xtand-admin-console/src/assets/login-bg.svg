<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grid" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.05"/>
    </linearGradient>
  </defs>
  
  <!-- 背景网格 -->
  <pattern id="smallGrid" width="30" height="30" patternUnits="userSpaceOnUse">
    <path d="M 30 0 L 0 0 0 30" fill="none" stroke="url(#grid)" stroke-width="0.5"/>
  </pattern>
  <pattern id="grid" width="150" height="150" patternUnits="userSpaceOnUse">
    <rect width="150" height="150" fill="url(#smallGrid)"/>
    <path d="M 150 0 L 0 0 0 150" fill="none" stroke="url(#grid)" stroke-width="1"/>
  </pattern>
  
  <!-- 背景矩形 -->
  <rect width="100%" height="100%" fill="url(#grid)"/>
  
  <!-- 科技线条和圆点 -->
  <g fill="none" stroke="#ffffff" stroke-width="1" opacity="0.1">
    <circle cx="150" cy="150" r="50"/>
    <circle cx="150" cy="150" r="45"/>
    <circle cx="150" cy="150" r="40"/>
    <line x1="150" y1="50" x2="150" y2="250"/>
    <line x1="50" y1="150" x2="250" y2="150"/>
  </g>
  
  <g fill="none" stroke="#ffffff" stroke-width="1" opacity="0.1">
    <circle cx="850" cy="450" r="100"/>
    <circle cx="850" cy="450" r="90"/>
    <circle cx="850" cy="450" r="80"/>
    <line x1="850" y1="300" x2="850" y2="600"/>
    <line x1="700" y1="450" x2="1000" y2="450"/>
  </g>
  
  <!-- 装饰性点阵 -->
  <g fill="#ffffff">
    <circle cx="100" cy="100" r="1" opacity="0.3"/>
    <circle cx="200" cy="200" r="1" opacity="0.3"/>
    <circle cx="300" cy="300" r="1" opacity="0.3"/>
    <circle cx="400" cy="200" r="1" opacity="0.3"/>
    <circle cx="500" cy="100" r="1" opacity="0.3"/>
    <circle cx="600" cy="300" r="1" opacity="0.3"/>
    <circle cx="700" cy="400" r="1" opacity="0.3"/>
    <circle cx="800" cy="200" r="1" opacity="0.3"/>
    <circle cx="900" cy="100" r="1" opacity="0.3"/>
  </g>
  
  <!-- 连接线 -->
  <g stroke="#ffffff" stroke-width="0.5" opacity="0.1">
    <line x1="100" y1="100" x2="200" y2="200"/>
    <line x1="200" y1="200" x2="300" y2="300"/>
    <line x1="300" y1="300" x2="400" y2="200"/>
    <line x1="400" y1="200" x2="500" y2="100"/>
    <line x1="500" y1="100" x2="600" y2="300"/>
    <line x1="600" y1="300" x2="700" y2="400"/>
    <line x1="700" y1="400" x2="800" y2="200"/>
    <line x1="800" y1="200" x2="900" y2="100"/>
  </g>
</svg> 