<template>
  <el-breadcrumb class="app-breadcrumb">
    <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index" :to="item.path">
      {{ item.meta?.title || item.name }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const breadcrumbs = ref([])

const getBreadcrumbs = () => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  breadcrumbs.value = matched
}

watch(() => route.path, getBreadcrumbs, { immediate: true })
</script>

<style lang="less" scoped>
.app-breadcrumb {
  margin-left: 8px;
  line-height: 60px;
  
  :deep(.el-breadcrumb__inner) {
    color: #666;
    
    &.is-link {
      color: #1976d2;
      font-weight: normal;
      
      &:hover {
        color: #1565c0;
      }
    }
  }
}
</style> 