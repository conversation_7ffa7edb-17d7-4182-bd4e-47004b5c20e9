<template>
  <div class="app-wrapper">
    <!-- 侧边栏 -->
    <div class="sidebar-container">
      <div class="logo-container">
        <img src="@/assets/logo.png" alt="Logo" class="logo-img">
        <span class="logo-text">智能APP管理系统</span>
      </div>
      <el-menu
        :default-active="activeMenu"
        class="el-menu-vertical"
        background-color="#ffffff"
        text-color="#333333"
        active-text-color="#1976d2"
        :unique-opened="true"
      >
        <el-menu-item index="/dashboard" @click="navigateTo('/dashboard')">
          <el-icon><House /></el-icon>
          <template #title>工作台</template>
        </el-menu-item>
      


        <template v-for="item in menus" :key="item.id">
    <el-sub-menu v-if="item.children && item.children.length" :index="item.path">
      <template #title>
        <el-icon v-if="item.icon"><i :class="item.icon"></i></el-icon>
        <span>{{ item.name }}</span>
      </template>
      <template v-for="child in item.children" :key="child.id">
        <el-menu-item :index="child.path" @click="navigateTo(child.path)">
          <el-icon v-if="child.icon"><i :class="child.icon"></i></el-icon>
          <span>{{ child.name }}</span>
        </el-menu-item>
      </template>
    </el-sub-menu>
    <el-menu-item v-else :index="item.path" @click="navigateTo(item.path)">
      <el-icon v-if="item.icon"><i :class="item.icon"></i></el-icon>
      <span>{{ item.name }}</span>
    </el-menu-item>
  </template>


              
      </el-menu>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
      <!-- 头部 -->
      <div class="app-header">
        <div class="header-left">
          <breadcrumb />
        </div>
        
        <div class="header-right">
          <el-tooltip content="全屏" placement="bottom">
            <el-icon class="header-icon" @click="toggleFullScreen">
              <FullScreen />
            </el-icon>
          </el-tooltip>
          
          <el-dropdown trigger="click">
            <div class="avatar-container">
              <el-avatar :size="32" :src="userAvatar" />
              <span class="user-name">{{ userName }}</span>
              <el-icon><CaretBottom /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleProfile">
                  <el-icon><User /></el-icon>用户中心
                </el-dropdown-item>
                <el-dropdown-item @click="handleLogout">
                  <el-icon><SwitchButton /></el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 主要内容区 -->
      <div class="app-main">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed,onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { useMenuStore } from '@/store/menu'
import {
  House,
  Setting,
  User,
  UserFilled,
  Menu,
  Connection,
  Files,
  Check,
  Fold,
  Expand,
  FullScreen,
  CaretBottom,
  SwitchButton,
  Plus,
  List,
  Document
} from '@element-plus/icons-vue'

const router = useRouter()
const isCollapse = ref(false)
const menuStore = useMenuStore()
const menus = computed(() => menuStore.menus)
const activeMenu = ref(router.currentRoute.value.path)

// 从localStorage获取用户信息
const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
const userName = computed(() => userInfo.userName || '未登录')
const userAvatar = computed(() => userInfo.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png')
const userRoles = computed(() => userInfo.role || [])


// 页面刷新时自动加载菜单（如已登录）
onMounted(() => {
  if (!menuStore.menus.length) {
    menuStore.loadMenuTree()
  }
})

const navigateTo = (path) => {
  router.push(path)
  activeMenu.value = path
}

const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const toggleFullScreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

const handleProfile = () => {
  router.push('/system/profile').catch(err => {
    // 可以打印或忽略 NavigationDuplicated 等错误
    if (err && err.name !== 'NavigationDuplicated') {
      console.error(err)
    }
  })
}

const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    router.push('/login')
  })
}
</script>

<style lang="less" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  display: flex;
  background-color: #f5f7fa;
  
  .sidebar-container {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 220px;
    background-color: #ffffff;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    z-index: 1001;

    .logo-container {
      height: 60px;
      padding: 10px 16px;
      display: flex;
      align-items: center;
      background: #ffffff;
      border-bottom: 1px solid #e8e8e8;

      .logo-img {
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }

      .logo-text {
        color: #1a237e;
        font-size: 18px;
        font-weight: 600;
      }
    }

    :deep(.el-menu) {
      border-right: none;

      .el-menu-item, .el-sub-menu__title {
        height: 50px;
        line-height: 50px;

        &:hover {
          background-color: #e6f2ff !important;
        }

        .el-icon {
          font-size: 18px;
          margin-right: 12px;
        }
      }

      .el-menu-item.is-active {
        background-color: #e6f2ff !important;
        border-right: 3px solid #1976d2;
      }
    }
  }

  .main-container {
    position: relative;
    min-height: 100vh;
    margin-left: 220px;
    flex: 1;
    background-color: #f5f7fa;
    display: flex;
    flex-direction: column;

    .app-header {
      position: sticky;
      top: 0;
      height: 60px;
      background-color: #ffffff;
      border-bottom: 1px solid #e8e8e8;
      padding: 0 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      z-index: 1000;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .header-left {
        display: flex;
        align-items: center;
      }

      .header-right {
        display: flex;
        align-items: center;

        .header-icon {
          font-size: 20px;
          margin-right: 20px;
          cursor: pointer;
          color: #666666;
          
          &:hover {
            color: #1976d2;
          }
        }

        .avatar-container {
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 0 8px;
          height: 40px;
          border-radius: 4px;
          transition: all 0.3s;

          &:hover {
            background-color: #e6f2ff;
          }

          .user-name {
            margin: 0 8px;
            color: #333333;
            font-size: 14px;
          }

          .el-icon {
            font-size: 12px;
            color: #666666;
          }
        }
      }
    }

    .app-main {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      box-sizing: border-box;
      background-color: #f5f7fa;

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: #f5f7fa;
      }
    }
  }
}

// 响应式布局
@media screen and (max-width: 768px) {
  .app-wrapper {
    .sidebar-container {
      transform: translateX(-220px);
    }

    .main-container {
      margin-left: 0 !important;
    }
  }
}
</style>