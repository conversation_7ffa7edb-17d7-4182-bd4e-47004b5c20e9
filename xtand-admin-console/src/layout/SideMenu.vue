<template>
  <el-menu :router="true" :default-active="$route.path">
    <template v-for="item in menuTree">
      <el-sub-menu v-if="item.children && item.children.length" :index="'submenu-' + item.id" :key="'submenu-' + item.id">
        <template #title>
          <i :class="item.icon"></i>
          <span>{{ item.name }}</span>
        </template>
        <side-menu :menu-tree="item.children" />
      </el-sub-menu>
      <el-menu-item v-else :index="item.path" :key="'menuitem-' + item.id">
        <i :class="item.icon"></i>
        <span>{{ item.name }}</span>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script setup>
import { useMenuStore } from '@/store/menu'
import { computed } from 'vue'
const menuTree = computed(() => useMenuStore().menuTree)
</script> 