import { createRouter, createWebHistory } from 'vue-router'
import { useMenuStore } from '@/store/menu'

const routes = [
  { path: '/login', component: () => import('@/views/login.vue') },
  {
    path: '/',
    component: () => import('@/components/layout/index.vue'),
    children: [
      { path: '', redirect: '/dashboard' },
      { path: 'dashboard', component: () => import('@/views/dashboard/index.vue') },
      { path: 'system/user', component: () => import('@/views/system/sysUser/index.vue') },
      { path: 'system/role', component: () => import('@/views/system/role/index.vue') },
      { path: 'system/menu', component: () => import('@/views/system/menu/index.vue') },
      { path: 'uservip/user', component: () => import('@/views/uservip/user/index.vue') }


    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由前置守卫：登录拦截、Token校验、动态菜单加载
router.beforeEach(async (to, from, next) => {
  const token = localStorage.getItem('token')
  if (to.path === '/login') {
    if (token) {
      next('/dashboard') // 已登录跳首页
    } else {
      next()
    }
    return
  }
  if (!token) {
    next('/login')
    return
  }
  // 动态菜单/路由只加载一次
  const menuStore = useMenuStore()
  if (!menuStore.menus.length) {
    await menuStore.loadMenuTree()
    next({ ...to, replace: true })
    return
  }
  next()
})

export default router 