<template>
  <div class="login-container">
    <div class="login-content">
      <div class="login-header">
        <h1 class="system-name">智能APP管理系统</h1>
      </div>
      
      <el-card class="login-card">
        <template #header>
          <div class="card-header">
            <!-- <img src="@/assets/logo.svg" alt="Logo" class="login-logo"> -->
             <!-- <img src="@/assets/login.png" alt="Logo" class="login-logo"> -->

            <h2 class="login-title">欢迎登录</h2>
          </div>
        </template>
        
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          label-width="0"
        >
          <el-form-item prop="userName">
            <el-input
              v-model="loginForm.userName"
              placeholder="请输入用户名或者手机号"
              prefix-icon="User"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              :loading="loading"
              class="login-button"
              @click="handleLogin"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <p class="system-slogan">科技赋能 · 智能高效</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Timer, Connection, DataAnalysis } from '@element-plus/icons-vue'
import { login } from '@/api/sysUser' 


const router = useRouter()

const loginForm = reactive({
  userName: '',
  password: ''
})

const loginRules = {
  userName: [
    { required: true, message: '请输入用户名或者手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

const loading = ref(false)
const loginFormRef = ref(null)

const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loading.value = true
    
    const data = await login({
      userName: loginForm.userName,
      password: loginForm.password
    })
    console.log(data);
    // 存储 token
    localStorage.setItem('token', data.token)
    // 存储用户信息
    localStorage.setItem('userInfo', JSON.stringify({
      userName: data.userName,
      isSuperAdmin: data.isSuperAdmin 

    }))
    
    ElMessage.success('登录成功')
    router.push('/')
  } catch (error) {
    console.error('登录失败:', error)
    // 错误消息已经在request.js中统一处理，这里不需要重复显示
  } finally {
    loading.value = false
  }
}
</script>

<style lang="less" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1a237e 0%, #0d47a1 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('@/assets/login-bg.svg') center/cover no-repeat;
    opacity: 0.1;
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%);
    z-index: 1;
  }
}

.login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  z-index: 2;
  padding: 40px;
  max-width: 400px;
  width: 100%;
}

.login-header {
  text-align: center;
  color: #ffffff;

  .system-name {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 0;
    background: linear-gradient(to right, #ffffff, #64b5f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.system-slogan {
  color: #ffffff;
  font-size: 18px;
  opacity: 0.8;
  margin: 0;
  text-align: center;
}

.login-card {
  width: 100%;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .card-header {
    text-align: center;
    padding: 20px 0;

    .login-logo {
      width: 100px;
      height: 100px;
      margin-bottom: 16px;
    }
  }

  .login-title {
    font-size: 24px;
    color: #1a237e;
    margin: 0;
  }

  :deep(.el-input) {
    .el-input__wrapper {
      background-color: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(25, 118, 210, 0.1);
      transition: all 0.3s;

      &:hover, &.is-focus {
        border-color: #1976d2;
        box-shadow: 0 0 0 1px rgba(25, 118, 210, 0.1);
      }
    }

    .el-input__prefix {
      color: #1976d2;
    }
  }

  .login-button {
    width: 100%;
    height: 40px;
    background: linear-gradient(45deg, #1976d2, #2196f3);
    border: none;
    font-size: 16px;
    
    &:hover {
      background: linear-gradient(45deg, #1565c0, #1976d2);
    }
  }
}

@media (max-width: 768px) {
  .login-content {
    padding: 20px;
  }
}
</style>