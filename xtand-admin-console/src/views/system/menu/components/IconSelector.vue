<template>
  <div class="icon-selector">
    <el-input 
      v-model="selectedIcon" 
      placeholder="请选择图标" 
      readonly
      @click="showDialog = true"
    >
      <template #prefix>
        <el-icon v-if="selectedIcon">
          <component :is="selectedIcon" />
        </el-icon>
      </template>
      <template #suffix>
        <el-icon style="cursor: pointer;" @click="showDialog = true">
          <Search />
        </el-icon>
      </template>
    </el-input>

    <el-dialog 
      title="选择图标" 
      v-model="showDialog" 
      width="800px"
      @close="handleClose"
    >
      <div class="icon-search">
        <el-input 
          v-model="searchText" 
          placeholder="搜索图标名称"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <div class="icon-grid">
        <div 
          v-for="icon in filteredIcons" 
          :key="icon"
          class="icon-item"
          :class="{ active: selectedIcon === icon }"
          @click="selectIcon(icon)"
        >
          <el-icon class="icon-display">
            <component :is="icon" />
          </el-icon>
          <div class="icon-name">{{ icon }}</div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="clearIcon">清除</el-button>
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmSelect">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { Search } from '@element-plus/icons-vue'
import * as ElementPlusIcons from '@element-plus/icons-vue'

export default {
  name: 'IconSelector',
  components: {
    Search,
    ...ElementPlusIcons
  },
  props: {
    modelValue: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue'],
  data() {
    return {
      showDialog: false,
      searchText: '',
      selectedIcon: this.modelValue,
      // Element Plus 常用图标列表
      iconList: [
        'User', 'Lock', 'Unlock', 'View', 'Hide', 'Search', 'Refresh',
        'Plus', 'Minus', 'Edit', 'Delete', 'Check', 'Close', 'ArrowLeft',
        'ArrowRight', 'ArrowUp', 'ArrowDown', 'Upload', 'Download', 'Share',
        'Star', 'StarFilled', 'Heart', 'HeartFilled', 'Like', 'Unlike',
        'Message', 'ChatDotRound', 'Bell', 'BellFilled', 'Warning', 'WarningFilled',
        'Question', 'QuestionFilled', 'Info', 'InfoFilled', 'Success', 'SuccessFilled',
        'Error', 'CircleClose', 'CircleCheck', 'CirclePlus', 'Remove', 'ZoomIn',
        'ZoomOut', 'Setting', 'Tools', 'Menu', 'More', 'MoreFilled',
        'Folder', 'FolderOpened', 'Document', 'DocumentCopy', 'Files', 'Tickets',
        'Calendar', 'Clock', 'Timer', 'AlarmClock', 'Stopwatch', 'Watch',
        'Location', 'Place', 'Position', 'MapLocation', 'Navigation', 'Compass',
        'Phone', 'PhoneFilled', 'Message', 'ChatLineRound', 'ChatRound', 'ChatSquare',
        'House', 'HomeFilled', 'School', 'Office', 'Shop', 'ShoppingCart',
        'Goods', 'Box', 'Present', 'Gift', 'Trophy', 'Medal',
        'Camera', 'Picture', 'PictureRounded', 'VideoCamera', 'Headset', 'Microphone',
        'Monitor', 'Cpu', 'HardDrive', 'Wifi', 'Connection', 'Link'
      ]
    }
  },
  computed: {
    filteredIcons() {
      if (!this.searchText) {
        return this.iconList
      }
      return this.iconList.filter(icon => 
        icon.toLowerCase().includes(this.searchText.toLowerCase())
      )
    }
  },
  watch: {
    modelValue(newVal) {
      this.selectedIcon = newVal
    }
  },
  methods: {
    selectIcon(icon) {
      this.selectedIcon = icon
    },
    
    confirmSelect() {
      this.$emit('update:modelValue', this.selectedIcon)
      this.showDialog = false
    },
    
    clearIcon() {
      this.selectedIcon = ''
      this.$emit('update:modelValue', '')
      this.showDialog = false
    },
    
    handleClose() {
      this.selectedIcon = this.modelValue
    }
  }
}
</script>

<style scoped>
.icon-search {
  margin-bottom: 20px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.icon-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.icon-item.active {
  border-color: #409eff;
  background-color: #409eff;
  color: white;
}

.icon-display {
  font-size: 24px;
  margin-bottom: 8px;
}

.icon-name {
  font-size: 12px;
  text-align: center;
  word-break: break-all;
}

.dialog-footer {
  text-align: right;
}
</style>
