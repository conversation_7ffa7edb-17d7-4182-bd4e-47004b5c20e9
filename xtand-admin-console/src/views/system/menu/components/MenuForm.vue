<template>
  <el-dialog :title="dialogTitle" :model-value="visible" @update:model-value="$emit('update:visible', $event)"  width="600px" @close="handleClose">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <!-- 上级菜单显示 -->
      <el-form-item label="上级菜单">
        <el-input :value="parentMenuName" disabled />
      </el-form-item>
      <el-form-item label="菜单名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入菜单名称" />
      </el-form-item>
      
      <!-- 菜单类型 - 显示类型但不可选择 -->
      <el-form-item label="菜单类型">
        <el-input :value="typeDisplayName" disabled />
      </el-form-item>
      
      <!-- 权限标识 -->
      <el-form-item v-if="form.type !== -1" label="权限标识" prop="permission">
        <!-- 编辑模式：直接显示完整权限编码 -->
        <template v-if="editData">
          <el-input 
            v-model="form.permission" 
            placeholder="请输入权限标识" 
          />
        </template>
        
        <!-- 新增模式：根据类型显示不同的输入方式 -->
        <template v-else>
          <!-- 按钮类型显示前缀+后缀的形式 -->
          <template v-if="form.type === 2">
            <div style="display: flex; align-items: center;">
              <span style="margin-right: 8px; color: #909399;">{{ parentPermissionPrefix }}</span>
              <el-input 
                v-model="permissionSuffix" 
                placeholder="如：add、edit、delete" 
                style="flex: 1;"
                @input="updateFullPermission"
              />
            </div>
            <div style="margin-top: 4px; color: #909399; font-size: 12px;">
              完整权限标识：{{ form.permission }}
            </div>
          </template>
          
          <!-- 目录和菜单类型 -->
          <template v-else>
            <div v-if="parentPermissionPrefix" style="display: flex; align-items: center;">
              <span style="margin-right: 8px; color: #909399;">{{ parentPermissionPrefix }}</span>
              <el-input 
                v-model="permissionSuffix" 
                :placeholder="getPermissionSuffixPlaceholder()" 
                style="flex: 1;"
                @input="updateFullPermission"
              />
            </div>
            <div v-else>
              <el-input 
                v-model="form.permission" 
                placeholder="如：user、role、system" 
              />
            </div>
            <div v-if="parentPermissionPrefix" style="margin-top: 4px; color: #909399; font-size: 12px;">
              完整权限标识：{{ form.permission }}
            </div>
          </template>
        </template>
      </el-form-item>
      
      <!-- 路由路径 - 按钮类型不需要 -->
      <el-form-item v-if="form.type !== 2" label="路由路径">
        <el-input v-model="form.path" placeholder="请输入路由地址" />
      </el-form-item>
      <el-form-item label="图标" prop="icon">
        <el-input v-model="form.icon" placeholder="请输入图标名" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="form.sort" :min="0" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:visible', false)">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addMenu, editMenu } from '@/api/sysMenu'

export default {
  name: 'MenuForm',
  props: {
    visible: Boolean,
    editData: Object,
    parentData: Object,
    childType: Number
  },
  data() {
    return {
      form: {
        parentId: 0,
        name: '',
        type: 1,
        permission: '',
        path: '',
        icon: '',
        sort: 0,
        status: 1,
        isSystem: 0
      },
      permissionSuffix: '', // 权限编码的后缀部分
      rules: {
        name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
        permission: [{ 
          required: true,
          validator: (rule, value, callback) => {
            if (this.form.type === 2 && !value) {
              callback(new Error('请输入权限标识'))
            } else {
              callback()
            }
          },
          trigger: 'blur' 
        }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      }
    }
  },
  computed: {
    dialogTitle() {
      if (this.editData) return '编辑菜单'
      const typeNames = { 0: '目录', 1: '菜单', 2: '按钮' }
      return `新增${typeNames[this.childType] || '菜单'}`
    },
    
    parentMenuName() {
      if (!this.parentData) return '无上级菜单'
      if (this.parentData.id === 0) return '根目录'
      if (this.parentData.isVirtual) return this.parentData.name + '（虚拟节点）'
      return this.parentData.name
    },
    
    typeDisplayName() {
      const typeNames = { 0: '目录', 1: '菜单', 2: '按钮' }
      return typeNames[this.form.type] || '未知'
    },
    
    // 获取父级权限编码前缀
    parentPermissionPrefix() {
      if (!this.parentData || this.parentData.isVirtual) {
        return '' // 虚拟节点没有权限前缀
      }
      
      // 如果父级有权限编码，使用父级的
      if (this.parentData.permission) {
        return this.parentData.permission + ':'
      }
      
      // 如果父级没有权限编码，根据层级生成
      if (this.parentData.type === 0) {
        // 父级是目录，使用目录名的拼音或简写作为前缀
        return this.generatePermissionPrefix(this.parentData.name) + ':'
      }
      
      return ''
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    },
    // 监听editData变化，重新初始化表单
    editData: {
      immediate: true,
      handler(val) {
        if (val) {
          this.initForm()
        }
      }
    },
    // 监听childType变化，确保类型正确设置（仅在新增模式下）
    childType: {
      immediate: true,
      handler(val) {
        if (val !== null && val !== undefined && !this.editData) {
          this.form.type = val
        }
      }
    }
  },
  created() {
    // 确保在组件创建时就设置正确的类型（仅在新增模式下）
    if (this.childType !== null && this.childType !== undefined && !this.editData) {
      this.form.type = this.childType
    }
  },
  methods: {
    initForm() {
      if (this.editData) {
        // 编辑模式：直接使用完整的权限编码，不需要拆分
        this.form = { ...this.editData }
        this.permissionSuffix = ''
      } else {
        // 新增模式
        this.form = {
          name: '',
          type: this.childType,
          parentId: this.getParentId(),
          isSystem: this.getIsSystemValue(),
          path: '',
          permission: this.getDefaultPermission(),
          icon: '',
          sort: 0,
          status: 1
        }
        this.permissionSuffix = ''
      }
    },
    
    getParentId() {
      if (!this.parentData) return null
      if (this.parentData.isVirtual) return null  // 虚拟节点的子项没有parentId
      return this.parentData.id !== 0 ? this.parentData.id : null
    },
    
    getIsSystemValue() {
      // 如果父级菜单是系统菜单，子菜单也是系统菜单
      if (this.parentData && this.parentData.isSystem === 1) {
        return 1
      }
      // 默认为业务菜单
      return 0
    },
    
    getDefaultPermission() {
      // 所有类型都通过前缀+后缀方式设置，初始为空
      return ''
    },

    
    // 获取权限编码后缀占位符
    getPermissionSuffixPlaceholder() {
      if (this.form.type === 0) {
        // 目录类型
        return '如：management、system'
      } else if (this.form.type === 1) {
        // 菜单类型
        return '如：list、detail'
      } else if (this.form.type === 2) {
        // 按钮类型
        return '如：add、edit、delete'
      }
      return '请输入权限后缀'
    },
    
    // 更新完整的权限编码（仅在新增模式下）
    updateFullPermission() {
      // 编辑模式不需要更新权限编码
      if (this.editData) return
      
      // 新增模式：所有有父级权限前缀的类型都使用前缀+后缀的方式
      if (this.parentPermissionPrefix) {
        this.form.permission = this.parentPermissionPrefix + this.permissionSuffix
      }
    },
    
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const api = this.editData ? editMenu : addMenu
        api(this.form).then(() => {
          this.$message.success('操作成功')
          this.$emit('update:visible', false)
          this.$emit('success')
        })
      })
    },
    
    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script> 