<template>
  <div class="menu-page">
    <!-- 操作栏 -->
    <!-- <div class="toolbar">
      <el-alert 
        title="操作提示：点击下方'工作台'节点的'新增目录'按钮来添加菜单" 
        type="info" 
        :closable="false"
      />
    </div> -->

    <!-- 菜单树表格 -->
    <el-table :data="displayMenuList" row-key="id" border default-expand-all :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column prop="name" label="菜单名称" />
      <el-table-column prop="permission" label="权限标识" />
      <el-table-column prop="path" label="路由" />
      <el-table-column prop="icon" label="图标" />
      <el-table-column prop="sort" label="排序" />
      <el-table-column label="菜单类型" width="100">
        <template #default="scope">
          <el-tag v-if="!scope.row.isVirtual" :type="scope.row.isSystem === 1 ? 'danger' : 'primary'">
            {{ scope.row.isSystem === 1 ? '系统菜单' : '业务菜单' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280">
        <template #default="scope">
          <!-- 虚拟节点只显示新增按钮 -->
          <template v-if="scope.row.isVirtual">
            <el-button 
              size="mini" 
              type="primary" 
              @click="openAddChild(scope.row)"
            >
              新增{{ getChildTypeName(scope.row) }}
            </el-button>
          </template>
          
          <!-- 普通菜单节点 -->
          <template v-else>
            <!-- 新增子项按钮 -->
            <el-button 
              v-if="canAddChild(scope.row)" 
              size="mini" 
              type="primary" 
              @click="openAddChild(scope.row)"
            >
              新增{{ getChildTypeName(scope.row) }}
            </el-button>
            
            <!-- 编辑按钮 -->
            <el-button 
              v-if="canEdit(scope.row)"
              size="mini" 
              @click="openEdit(scope.row)"
            >
              编辑
            </el-button>
            
            <!-- 删除按钮 -->
            <el-button 
              v-if="canDelete(scope.row)"
              size="mini" 
              type="danger" 
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑菜单弹窗 -->
    <menu-form
      v-if="showForm"
      v-model:visible="showForm"
      :edit-data="editData"
      :parent-data="parentData"
      :child-type="childType"
      @success="fetchList"
    />
  </div>
</template>

<script>
import { fetchMenuList, deleteMenu } from '@/api/sysMenu'
import MenuForm from './components/MenuForm.vue'
export default {
  name: 'MenuIndex',
  components: { MenuForm },
  data() {
    return {
      menuList: [],
      showForm: false,
      editData: null,
      parentData: null,
      childType: null,
      user: this.getCurrentUser()
    }
  },
  computed: {
        // 当前用户是否为超级管理员
    isSuperAdmin() {
      return this.user && this.user.isSuperAdmin === 1
    },
    
    // 生成包含虚拟节点的菜单树
    displayMenuList() {
      // 只保留工作台虚拟节点，包含所有菜单
      const allMenus = this.menuList.filter(menu => !menu.parentId)
      
      return [{
        id: 'virtual-workspace',
        name: '工作台',
        isVirtual: true,
        isSystem: false,
        status:1,
        type: -1, // 虚拟节点类型
        children: allMenus
      }]
    }
  },
  methods: {
    // 获取当前用户信息
    getCurrentUser() {
      try {
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
        console.log("getCurrentUser",userInfo);
        return {
          userName: userInfo.userName || '',
          isSuperAdmin: userInfo.isSuperAdmin || 0
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        return { userName: '', isSuperAdmin: 0 }
      }
    },
    fetchList() {
      fetchMenuList().then(res => {
        this.menuList = res
      })
    },

    
    // 新增子项
    openAddChild(parentRow, childType = null) {
      // 系统菜单权限检查
      if (parentRow.isSystem === 1 && !this.isSuperAdmin) {
        this.$message.warning('只有超级管理员才能操作系统菜单')
        return
      }
      
      // 自动确定子项类型
      if (childType === null) {
        if (parentRow.isVirtual) {
          childType = 0  // 虚拟节点下新增目录
        } else {
          childType = parentRow.type + 1
        }
      }
      
      this.editData = null
      this.parentData = parentRow
      this.childType = childType
      this.showForm = true
    },
    
    // 编辑菜单
    openEdit(row) {
      // 系统菜单权限检查
      if (row.isSystem === 1 && !this.isSuperAdmin) {
        this.$message.warning('只有超级管理员才能编辑系统菜单')
        return
      }
      
      this.editData = row
      this.parentData = this.findParentMenu(row)
      this.childType = null
      this.showForm = true
    },
    
    // 删除菜单
    handleDelete(row) {
      // 系统菜单权限检查
      if (row.isSystem === 1 && !this.isSuperAdmin) {
        this.$message.warning('只有超级管理员才能删除系统菜单')
        return
      }
      
      if (this.hasChildren(row)) {
        this.$message.warning('请先删除子菜单')
        return
      }
      
      this.$confirm('确定要删除该菜单吗？', '提示', { type: 'warning' })
        .then(() => deleteMenu(row.id))
        .then(() => {
          this.$message.success('删除成功')
          this.fetchList()
        })
    },
    
    // 权限控制方法
    canAddChild(row) {
      // 虚拟节点可以新增子项
      if (row.isVirtual) return true
      
      // 按钮类型不能新增子项
      if (row.type === 2) return false
      
      // 系统菜单权限检查：只有超级管理员才能在系统菜单下新增子项
      if (row.isSystem === 1 && !this.isSuperAdmin) return false
      
      return true
    },
    
    canEdit(row) {
      // 虚拟节点不能编辑
      if (row.isVirtual) return false
      
      // 系统菜单权限检查：只有超级管理员才能编辑系统菜单
      if (row.isSystem === 1 && !this.isSuperAdmin) return false
      
      return true
    },
    
    canDelete(row) {
      // 虚拟节点不能删除
      if (row.isVirtual) return false
      
      // 有子菜单不能删除
      if (this.hasChildren(row)) return false
      
      // 系统菜单权限检查：只有超级管理员才能删除系统菜单
      if (row.isSystem === 1 && !this.isSuperAdmin) return false
      
      return true
    },
    
    // 获取子项类型名称
    getChildTypeName(row) {
      if (row.isVirtual) return '目录'   // 虚拟节点下新增目录
      if (row.type === 0) return '菜单'  // 目录下新增菜单
      if (row.type === 1) return '按钮'  // 菜单下新增按钮
      return ''
    },
    
    // 判断是否有子项
    hasChildren(row) {
      return row.children && row.children.length > 0
    },
    
    // 查找父级菜单（递归查找）
    findParentMenu(row) {
      if (!row.parentId) {
        // 如果没有parentId，说明是顶级菜单，父级是虚拟节点
        return {
          id: 'virtual-workspace',
          name: '工作台',
          isVirtual: true,
          type: -1
        }
      }
      
      // 递归查找父级菜单
      const findInTree = (menuList, targetId) => {
        for (let menu of menuList) {
          if (menu.id === targetId) {
            return menu
          }
          if (menu.children && menu.children.length > 0) {
            const found = findInTree(menu.children, targetId)
            if (found) return found
          }
        }
        return null
      }
      
      return findInTree(this.menuList, row.parentId)
    }
  },
  mounted() {
    this.fetchList()
  }
}
</script>

<style scoped>
.menu-page { padding: 24px; }
.toolbar { margin-bottom: 12px; }
</style> 