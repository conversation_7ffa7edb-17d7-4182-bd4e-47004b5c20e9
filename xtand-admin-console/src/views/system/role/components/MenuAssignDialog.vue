<template>
  <el-dialog title="分配菜单权限" :model-value="visible" @update:model-value="$emit('update:visible', $event)"  width="500px" @close="handleClose">
    <!-- 搜索框 -->
    <div style="margin-bottom: 16px;">
      <el-input
        v-model="filterText"
        placeholder="搜索菜单..."
        prefix-icon="Search"
        clearable
        style="width: 100%;"
      />
    </div>
    
    <!-- 权限提示 -->
    <el-alert 
      v-if="!canAssignSystemMenus"
      :title="getPermissionTip()" 
      type="info" 
      :closable="false"
      style="margin-bottom: 16px;"
    />
    
    <!-- 菜单树 -->
    <el-tree
      ref="treeRef"
      :data="filteredMenuList"
      show-checkbox
      node-key="id"
      :default-checked-keys="checkedKeys"
      :props="treeProps"
      :filter-node-method="filterNode"
      highlight-current
      default-expand-all
      style="max-height: 400px; overflow-y: auto;"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <!-- 菜单图标 -->
          <el-icon v-if="data.type === 1" style="margin-right: 4px; color: #909399;">
            <Folder />
          </el-icon>
          <el-icon v-else-if="data.type === 2" style="margin-right: 4px; color: #409eff;">
            <Document />
          </el-icon>
          <el-icon v-else-if="data.type === 3" style="margin-right: 4px; color: #67c23a;">
            <Operation />
          </el-icon>
          
          <!-- 菜单名称 -->
          <span>{{ node.label }}</span>
          
          <!-- 菜单类型标识 -->
          <el-tag 
            v-if="data.isSystem === 1" 
            type="danger" 
            size="small" 
            style="margin-left: 8px;"
          >
            系统
          </el-tag>
          <el-tag 
            v-else 
            type="primary" 
            size="small" 
            style="margin-left: 8px;"
          >
            业务
          </el-tag>
        </span>
      </template>
    </el-tree>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { fetchMenuList, fetchRoleMenuIds, assignMenu } from '@/api/sysMenu'
import { Folder, Document, Operation } from '@element-plus/icons-vue'

export default {
  name: 'MenuAssignDialog',
  components: {
    Folder,
    Document,
    Operation
  },
  props: {
    visible: Boolean,
    roleId: Number,
    roleInfo: Object
  },
  data() {
    return {
      menuList: [],
      filteredMenuList: [],
      checkedKeys: [],
      filterText: '',
      treeProps: {
        children: 'children',
        label: 'name'
      }
    }
  },
  computed: {
    // 判断当前用户是否为超级管理员
    isSuperAdmin() {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
      return userInfo.isSuperAdmin === 1
    },
    // 判断目标角色是否为系统角色
    isTargetSystemRole() {
      return this.roleInfo && this.roleInfo.isSystem === 1
    },
    // 是否可以分配系统菜单：当前用户是超级管理员 且 目标角色是系统角色
    canAssignSystemMenus() {
      return this.isSuperAdmin && this.isTargetSystemRole
    }
  },
  watch: {
    visible(val) {
      if (val && this.roleId) {
        this.loadMenus()
      }
    },
    filterText(val) {
      this.$refs.treeRef?.filter(val)
    }
  },
  mounted() {
    if (this.visible && this.roleId) {
      this.loadMenus()
    }
  },
  methods: {
    loadMenus() {
      fetchMenuList().then(res => {
        console.log("fetchMenuList res", res);
        this.menuList = res || []
        // 根据用户权限和角色类型过滤菜单
        this.filteredMenuList = this.filterMenusByPermission(this.menuList)
        this.loadRoleMenus()
      })
    },
    
    // 根据用户权限和角色类型过滤菜单
    filterMenusByPermission(menuList) {
      if (this.canAssignSystemMenus) {
        // 超级管理员给系统角色分配权限：可以看到所有菜单
        return menuList
      } else {
        // 其他情况：只能看到业务菜单
        return this.filterSystemMenus(menuList)
      }
    },
    
    // 获取权限提示文本
    getPermissionTip() {
      if (!this.isSuperAdmin) {
        return '提示：您只能分配业务菜单权限，系统菜单权限需要超级管理员操作'
      } else if (!this.isTargetSystemRole) {
        return '提示：当前角色为业务角色，只能分配业务菜单权限。系统菜单只能分配给系统角色'
      }
      return '提示：您只能分配业务菜单权限'
    },
    
    // 递归过滤系统菜单
    filterSystemMenus(menuList) {
      return menuList.filter(menu => {
        // 过滤掉系统菜单
        if (menu.isSystem === 1) {
          return false
        }
        // 递归处理子菜单
        if (menu.children && menu.children.length > 0) {
          menu.children = this.filterSystemMenus(menu.children)
        }
        return true
      })
    },
    
    loadRoleMenus() {
      if (!this.roleId) return
      fetchRoleMenuIds(this.roleId).then(res => {
        this.checkedKeys = res || []
        this.$nextTick(() => {
          this.$refs.treeRef?.setCheckedKeys(this.checkedKeys)
        })
      })
    },
    
    // 搜索过滤方法
    filterNode(value, data) {
      if (!value) return true
      return data.name.toLowerCase().includes(value.toLowerCase())
    },
    
    handleSubmit() {
      // 获取选中的节点（包括半选状态的父节点）
      const checkedNodes = this.$refs.treeRef.getCheckedNodes()
      const halfCheckedNodes = this.$refs.treeRef.getHalfCheckedNodes()
      
      // 合并完全选中和半选中的节点ID
      const allCheckedIds = [
        ...checkedNodes.map(node => node.id),
        ...halfCheckedNodes.map(node => node.id)
      ]
      
      assignMenu(this.roleId, allCheckedIds).then(() => {
        this.$message.success('分配成功')
        this.$emit('update:visible', false)
        this.$emit('success')
      }).catch(error => {
        console.error('分配菜单权限失败:', error)
        this.$message.error('分配失败，请重试')
      })
    },
    
    handleClose() {
      this.filterText = ''
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
.custom-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-tree-node__content) {
  height: 32px;
}
</style> 