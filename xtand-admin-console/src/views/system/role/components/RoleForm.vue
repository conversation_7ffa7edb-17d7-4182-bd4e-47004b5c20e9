<template>
  <el-dialog :title="editData ? '编辑角色' : '新增角色'" :model-value="visible" @update:model-value="$emit('update:visible', $event)"  width="400px" @close="handleClose">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="90px">
      <el-form-item label="角色名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入角色名称" />
      </el-form-item>
      <el-form-item label="角色编码" prop="code">
        <el-input v-model="form.code" placeholder="请输入角色编码（如 admin）" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" placeholder="请输入描述" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addRole, editRole } from '@/api/sysRole'

export default {
  name: 'RoleForm',
  props: {
    visible: Boolean,
    editData: Object
  },
  data() {
    return {
      form: {
        name: '',
        code: '',
        description: '',
        status: 1
      },
      rules: {
        name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入角色编码', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      }
    }
  },
  watch: {
    editData: {
      handler(val) {
        if (val) {
          this.form = { ...val }
        } else {
          this.form = { name: '', code: '', description: '', status: 1 }
        }
      },
      immediate: true
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const api = this.editData ? editRole : addRole
        api(this.form).then(() => {
          this.$message.success('操作成功')
          this.$emit('update:visible', false)
          this.$emit('success')
        })
      })
    },
    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script> 