<template>
  <div class="role-page">
    <!-- 搜索栏 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="角色名称">
        <el-input v-model="searchForm.name" placeholder="请输入角色名称" />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 150px;">
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="fetchList">
            <el-icon><Search /></el-icon>
          查询</el-button>
        <el-button @click="resetSearch">
          <el-icon><Refresh /></el-icon>
          重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="openAdd">
           <el-icon><Plus /></el-icon>
        新增角色</el-button>
    </div>

    <!-- 角色列表 -->
    <el-table :data="roleList" border stripe>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="name" label="角色名称" />
      <el-table-column prop="code" label="角色编码" />
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="status" label="状态" min-width="100" >
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="类型" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.isSystem" type="danger">系统内置</el-tag>
          <el-tag v-else type="success">普通角色</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="320">
        <template #default="scope">
          <!-- 系统内置角色：只有超级管理员能操作 -->
          <template v-if="scope.row.isSystem">
            <el-button size="mini" disabled  @click="openEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="warning" :disabled="!isSuperAdmin" @click="openAssignMenu(scope.row)">分配菜单</el-button>
            <el-button size="mini" type="danger" disabled @click="handleDelete(scope.row)">删除</el-button>
          </template>
          <!-- 普通角色：所有管理员都能操作 -->
          <template v-else>
            <el-button size="mini" @click="openEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="warning" @click="openAssignMenu(scope.row)">分配菜单</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pageNum"
      v-model:page-size="pageSize"
      :total="total"
      @current-change="fetchList"
      @size-change="fetchList"
      layout="total, prev, pager, next, sizes"
      :page-sizes="[10, 20, 50, 100]"
      class="pagination"
    />

    <!-- 新增/编辑角色弹窗 -->
    <role-form
      v-if="showForm"
       v-model:visible="showForm"
      :edit-data="editData"
      @success="fetchList"
    />



    <!-- 分配菜单弹窗 -->
    <menu-assign-dialog
      v-if="showAssignMenu"
       v-model:visible="showAssignMenu"
      :role-id="assignRoleId"
      :role-info="assignRoleInfo"
      @success="fetchList"
    />
  </div>
</template>

<script>
import { fetchRoleList, deleteRole } from '@/api/sysRole'
import RoleForm from './components/RoleForm.vue'
import MenuAssignDialog from './components/MenuAssignDialog.vue'

export default {
  name: 'RoleIndex',
  components: { RoleForm, MenuAssignDialog },
  data() {
    return {
      searchForm: {
        name: '',
        status: null
      },
      roleList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      showForm: false,
      editData: null,
      showAssignMenu: false,
      assignRoleId: null,
      assignRoleInfo: null,
      user: this.getCurrentUser()
    }
  },
  computed: {
    // 当前用户是否为超级管理员
    isSuperAdmin() {
      return this.user && this.user.isSuperAdmin === 1
    }
  },
  methods: {
    // 获取当前用户信息
    getCurrentUser() {
      try {
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
        return {
          userName: userInfo.userName || '',
          isSuperAdmin: userInfo.isSuperAdmin || 0
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        return { userName: '', isSuperAdmin: 0 }
      }
    },
    fetchList() {
      const params = {
        ...this.searchForm,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      fetchRoleList(params).then(res => {
        this.roleList = res.list
        this.total = res.total
      })
    },
    resetSearch() {
      this.searchForm = { name: '', status: null }
      this.fetchList()
    },
    openAdd() {
      this.editData = null
      this.showForm = true
    },
    openEdit(row) {
      // 系统内置角色权限检查
      if (row.isSystem && !this.isSuperAdmin) {
        this.$message.warning('只有超级管理员才能编辑系统内置角色')
        return
      }
      this.editData = row
      this.showForm = true
    },
    openAssignMenu(row) {
      // 系统内置角色权限检查
      if (row.isSystem && !this.isSuperAdmin) {
        this.$message.warning('只有超级管理员才能给系统内置角色分配菜单')
        return
      }
      this.assignRoleId = row.id
      this.assignRoleInfo = row
      this.showAssignMenu = true
    },
    handleDelete(row) {
      // 系统内置角色权限检查
      if (row.isSystem && !this.isSuperAdmin) {
        this.$message.warning('只有超级管理员才能删除系统内置角色')
        return
      }
      this.$confirm('确定要删除该角色吗？', '提示', { type: 'warning' })
        .then(() => deleteRole(row.id))
        .then(() => {
          this.$message.success('删除成功')
          this.fetchList()
        })
    }
  },
  mounted() {
    this.fetchList()
  }
}
</script>

<style scoped>
.role-page { padding: 24px; }
.search-form { margin-bottom: 16px; }
.toolbar { margin-bottom: 12px; }
.pagination { margin-top: 16px; text-align: right; }
</style> 