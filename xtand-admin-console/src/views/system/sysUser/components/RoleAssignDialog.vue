<template>
  <el-dialog title="分配角色" :visible.sync="visible" width="400px" @close="handleClose">
    <el-form>
      <el-form-item label="角色">
        <el-select v-model="roleIdList" multiple placeholder="请选择角色">
          <el-option v-for="role in roleList" :key="role.id" :label="role.name" :value="role.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { assignRoles, getUserRoles } from '@/api/sysUser'
import { fetchRoleList } from '@/api/sysRole'

export default {
  name: 'RoleAssignDialog',
  props: {
    visible: Boolean,
    userId: Number
  },
  data() {
    return {
      roleIdList: [],
      roleList: []
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadRoles()
        this.loadUserRoles()
      }
    },
    userId(val) {
      if (this.visible && val) this.loadUserRoles()
    }
  },
  methods: {
    loadRoles() {
      fetchRoleList().then(res => {
        this.roleList = res|| []
      })
    },
    loadUserRoles() {
      if (!this.userId) return
      getUserRoles(this.userId).then(res => {
        this.roleIdList = res.data.roleIdList || []
      })
    },
    handleSubmit() {
      assignRoles({ userId: this.userId, roleIdList: this.roleIdList }).then(() => {
        this.$message.success('分配成功')
        this.$emit('update:visible', false)
        this.$emit('success')
      })
    },
    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script> 