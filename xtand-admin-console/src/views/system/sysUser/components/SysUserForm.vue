<template>
  <el-dialog :title="editData ? '编辑用户' : '新增用户'" :model-value="visible" @update:model-value="$emit('update:visible', $event)" width="500px" @close="handleClose">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="90px">
      <el-form-item label="用户名" prop="userName">
        <el-input v-model="form.userName" :disabled="!!editData" placeholder="请输入用户名" />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="form.email" placeholder="请输入邮箱" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="角色" prop="roleIdList">
        <el-select v-model="form.roleIdList" multiple placeholder="请选择角色">
          <el-option v-for="role in roleList" :key="role.id" :label="role.name" :value="role.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { addSysUser, editSysUser } from '@/api/sysUser'
// 假设有角色API
import { fetchAllRoles } from '@/api/sysRole'

export default {
  name: 'SysUserForm',
  props: {
    visible: Boolean,
    editData: Object
  },
  data() {
    return {
      form: {
        userName: '',
        phone: '',
        email: '',
        status: 1,
        roleIdList: []
      },
      rules: {
        userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        roleIdList: [{ required: true, message: '请选择角色', trigger: 'change' }]
      },
      roleList: []
    }
  },
  watch: {
    editData: {
      handler(val) {
        if (val) {
          this.form = { ...val, roleIdList: val.roleIdList || [] }
        } else {
          this.form = { userName: '', phone: '', email: '', status: 1, roleIdList: [] }
        }
      },
      immediate: true
    },
    visible(val) {
      console.log("SysUserForm visible changed", val)
      if (val) this.loadRoles()
    }
  },
  mounted() {
    console.log("SysUserForm mounted", this.visible)
  },
  methods: {
    loadRoles() {
      fetchAllRoles().then(res => {
        console.log("fetchAllRoles res", res);
        this.roleList = res;
      })
    },
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const api = this.editData ? editSysUser : addSysUser
        api(this.form).then(() => {
          this.$message.success('操作成功')
          this.$emit('update:visible', false)
          this.$emit('success')
        })
      })
    },
    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script> 