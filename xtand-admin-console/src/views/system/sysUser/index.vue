<template>
  <div class="sys-user-page">
    <!-- 搜索栏 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="用户名">
        <el-input v-model="searchForm.username" placeholder="请输入用户名" />
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="searchForm.phone" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="fetchList">
           <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button @click="resetSearch">
        <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" :icon="Plus" @click="openAdd">
         <el-icon><Plus /></el-icon>
        新增用户</el-button>
    </div>

    <!-- 用户列表 -->
    <el-table :data="userList" border stripe>
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="userName" label="用户名"  min-width="100" />
      <el-table-column prop="phone" label="手机号"  min-width="100"/>
      <el-table-column prop="email" label="邮箱" min-width="180"  />
      <el-table-column prop="status" label="状态" min-width="100" >
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="roleNames" label="角色" >
      </el-table-column>
      <el-table-column label="操作" width="320">
        <template #default="scope">
          <el-button size="mini" @click="openEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="warning" @click="openResetPassword(scope.row)">重置密码</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)" :disabled="scope.row.isSuperAdmin">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pageNum"
      v-model:page-size="pageSize"
      :total="total"
      @current-change="fetchList"
      @size-change="fetchList"
      layout="total, prev, pager, next, sizes"
      :page-sizes="[10, 20, 50, 100]"
      class="pagination"
    />

    <!-- 新增/编辑用户弹窗 -->
    <sys-user-form
      v-model:visible="showForm"
      :edit-data="editData"
      @success="fetchList"
    />

    <!-- 分配角色弹窗 -->
    <role-assign-dialog
      v-if="showAssignRole"
      :visible.sync="showAssignRole"
      :user-id="assignUserId"
      @success="fetchList"
    />
  </div>
</template>

<script>
import { fetchSysUserList, deleteSysUser, resetUserPassword } from '@/api/sysUser'
import SysUserForm from './components/SysUserForm.vue'
import RoleAssignDialog from './components/RoleAssignDialog.vue'
import {
  Search,
  Refresh,
  Plus,
  Delete,
  Edit,
  Key,
  ArrowDown
} from '@element-plus/icons-vue'

export default {
  name: 'SysUserIndex',
  components: { 
    SysUserForm, 
    RoleAssignDialog
  },
  data() {
    return {
      searchForm: {
        userName: '',
        phone: ''
      },
      userList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      showForm: false,
      editData: null,
      showAssignRole: false,
      assignUserId: null
    }
  },
  methods: {
    fetchList() {
      const params = {
        ...this.searchForm,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      fetchSysUserList(params).then(res => {
        this.userList = res.list
        this.total = res.total
      })
    },
    resetSearch() {
      this.searchForm = { userName: '', phone: '' }
      this.fetchList()
    },
    openAdd() {
      console.log("openAdd")
      this.editData = null
      this.showForm = true
      this.$nextTick(() => {
      console.log("showForm after set", this.showForm)
    })
    },
    openEdit(row) {
      this.editData = row
      this.showForm = true
    },
    openAssignRole(row) {
      this.assignUserId = row.id
      this.showAssignRole = true
    },
    handleDelete(row) {
      this.$confirm('确定要删除该用户吗？', '提示', { type: 'warning' })
        .then(() => deleteSysUser(row.id))
        .then(() => {
          this.$message.success('删除成功')
          this.fetchList()
        })
    },
    openResetPassword(row) {
      this.$confirm('确定要重置该用户密码吗？', '提示', { type: 'warning' })
        .then(() => resetUserPassword(row.id))
        .then(() => {
          this.$message.success('重置成功')
        })
    }
  },
  mounted() {
    this.fetchList()
  }
}
</script>

<style scoped>
.sys-user-page { padding: 24px; }
.search-form { margin-bottom: 16px; }
.toolbar { margin-bottom: 12px; }
.pagination { margin-top: 16px; text-align: right; }
</style> 