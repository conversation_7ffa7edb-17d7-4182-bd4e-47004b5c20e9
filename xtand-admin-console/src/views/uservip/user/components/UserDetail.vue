<template>
  <el-dialog 
    title="用户详情" 
    :model-value="visible" 
    @update:model-value="$emit('update:visible', $event)" 
    width="600px"
    @close="handleClose"
  >
    <div v-loading="loading" class="user-detail">
      <div v-if="userInfo" class="detail-content">
        <!-- 用户头像和基本信息 -->
        <div class="user-header">
          <div class="avatar-section">
            <img 
              :src="avatarUrl" 
              :alt="userInfo.nickName"
              class="user-avatar"
              @error="handleImageError"
            />
          </div>
          <div class="basic-info">
            <h3 class="user-name">{{ userInfo.nickName || '未设置昵称' }}</h3>
            <div class="user-id">用户ID: {{ userInfo.id }}</div>
            <el-tag :type="userInfo.status === 1 ? 'success' : 'info'" class="status-tag">
              {{ userInfo.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </div>
        </div>

        <!-- 详细信息 -->
        <div class="detail-sections">
          <!-- 联系信息 -->
          <div class="info-section">
            <h4 class="section-title">联系信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>邮箱地址:</label>
                <span>{{ userInfo.email || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>手机号:</label>
                <span>{{ userInfo.mobile || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>地址:</label>
                <span>{{ userInfo.address || '未设置' }}</span>
              </div>
            </div>
          </div>

          <!-- 个人信息 -->
          <div class="info-section">
            <h4 class="section-title">个人信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>性别:</label>
                <span>
                  <el-tag :type="userInfo.sex === 1 ? 'primary' : 'success'" size="small">
                    {{ userInfo.sex === 1 ? '男' : '女' }}
                  </el-tag>
                </span>
              </div>
              <div class="info-item">
                <label>出生日期:</label>
                <span>{{ userInfo.birthday || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>身高:</label>
                <span>{{ userInfo.height ? userInfo.height + ' cm' : '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>体重:</label>
                <span>{{ userInfo.weight ? userInfo.weight + ' kg' : '未设置' }}</span>
              </div>
            </div>
          </div>

          <!-- 账户信息 -->
          <div class="info-section">
            <h4 class="section-title">账户信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>微信标识:</label>
                <span>{{ userInfo.openid || '未绑定' }}</span>
              </div>
              <div class="info-item">
                <label>苹果标识:</label>
                <span>{{ userInfo.appleid || '未绑定' }}</span>
              </div>
              <div class="info-item">
                <label>创建时间:</label>
                <span>{{ userInfo.createdTime || '未知' }}</span>
              </div>
              <div class="info-item">
                <label>更新时间:</label>
                <span>{{ userInfo.updatedTime || '未知' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { fetchUserDetail } from '@/api/user'
import defaultAvatar from '@/assets/default-avatar.png'

export default {
  name: 'UserDetail',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userId: {
      type: [Number, String],
      default: null
    }
  },
  emits: ['update:visible'],
  data() {
    return {
      userInfo: null,
      loading: false,
      imageError: false
    }
  },
  computed: {
    avatarUrl() {
      if (this.imageError || !this.userInfo?.avatar) {
        return defaultAvatar
      }
      return this.userInfo.avatar
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.userId) {
        this.fetchUserInfo()
      }
    },
    userId(newVal) {
      if (newVal && this.visible) {
        this.fetchUserInfo()
      }
    }
  },
  methods: {
    async fetchUserInfo() {
      if (!this.userId) return
      
      this.loading = true
      this.imageError = false
      
      try {
        const response = await fetchUserDetail(this.userId)
        this.userInfo = response
      } catch (error) {
        this.$message.error('获取用户详情失败')
        console.error('获取用户详情失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    handleImageError() {
      this.imageError = true
    },
    
    handleClose() {
      this.userInfo = null
      this.imageError = false
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
.user-detail {
  min-height: 200px;
}

.user-header {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.avatar-section {
  margin-right: 20px;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e4e7ed;
}

.basic-info {
  flex: 1;
}

.user-name {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.user-id {
  color: #909399;
  font-size: 14px;
  margin-bottom: 8px;
}

.status-tag {
  font-size: 12px;
}

.detail-sections {
  max-height: 400px;
  overflow-y: auto;
}

.info-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-left: 3px solid #409eff;
  padding-left: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.info-item label {
  min-width: 80px;
  color: #606266;
  font-weight: 500;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
  word-break: break-all;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .user-header {
    flex-direction: column;
    text-align: center;
  }
  
  .avatar-section {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>
