<template>
  <div class="user-page">
    <!-- 搜索栏 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="邮箱地址">
        <el-input v-model="searchForm.email" placeholder="请输入邮箱地址" clearable />
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="searchForm.mobile" placeholder="请输入手机号" clearable />
      </el-form-item>
      <el-form-item label="昵称">
        <el-input v-model="searchForm.nickName" placeholder="请输入昵称" clearable />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="searchForm.status" placeholder="请选择状态" style="width: 100px;" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button @click="resetSearch">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 会员列表 -->
    <el-table :data="userList" border stripe v-loading="loading">
      <el-table-column prop="id" label="用户ID" width="80" />
      <el-table-column prop="nickName" label="昵称" min-width="120" show-overflow-tooltip />
      <el-table-column prop="email" label="邮箱地址" min-width="180" show-overflow-tooltip  />
      <el-table-column prop="mobile" label="手机号" min-width="120" />
      <el-table-column prop="sex" label="性别" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.sex === 1 ? 'primary' : 'success'">
            {{ scope.row.sex === 1 ? '男' : '女' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="birthday" label="出生日期" width="120" />
      <!-- <el-table-column prop="height" label="身高(cm)" width="100" />
      <el-table-column prop="weight" label="体重(KG)" width="100" /> -->
      <el-table-column prop="address" label="地址" min-width="150" show-overflow-tooltip />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdTime" label="注册时间" width="180" />
      <el-table-column label="操作" width="280">
        <template #default="scope">
          <el-button size="mini" type="primary" @click="handleViewDetail(scope.row)">
            <el-icon><View /></el-icon>
            查看详情
          </el-button>
          <el-button
            size="mini"
            :type="scope.row.status === 1 ? 'warning' : 'success'"
            @click="handleStatusChange(scope.row)"
          >
            <el-icon>
              <component :is="scope.row.status === 1 ? 'Lock' : 'Unlock'" />
            </el-icon>
            {{ scope.row.status === 1 ? '禁用' : '启用' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pageNum"
      v-model:page-size="pageSize"
      :total="total"
      @current-change="fetchList"
      @size-change="fetchList"
      layout="total, prev, pager, next, sizes"
      :page-sizes="[10, 20, 50, 100]"
      class="pagination"
    />

    <!-- 用户详情弹窗 -->
    <user-detail
      v-model:visible="showDetailDialog"
      :user-id="selectedUserId"
    />
  </div>
</template>

<script>
import { fetchUserList, updateUserStatus } from '@/api/user'
import { Search, Refresh, View, Lock, Unlock, User } from '@element-plus/icons-vue'
import UserDetail from './components/UserDetail.vue'

export default {
  name: 'UserIndex',
  components: {
    Search,
    Refresh,
    View,
    Lock,
    Unlock,
    User,
    UserDetail
  },
  data() {
    return {
      searchForm: {
        email: '',
        mobile: '',
        nickName: '',
        status: ''
      },
      userList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      showDetailDialog: false,
      selectedUserId: null
    }
  },
  mounted() {
    this.fetchList()
  },
  methods: {
    // 获取会员列表
    fetchList() {
      this.loading = true
      const params = {
        ...this.searchForm,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      
      // 过滤空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key]
        }
      })
      
      fetchUserList(params).then(res => {
        this.userList = res.list || []
        this.total = res.total || 0
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    
    // 搜索
    handleSearch() {
      this.pageNum = 1
      this.fetchList()
    },
    
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        email: '',
        mobile: '',
        nickName: '',
        status: ''
      }
      this.pageNum = 1
      this.fetchList()
    },
    
    // 状态切换
    handleStatusChange(row) {
      const newStatus = row.status === 1 ? 0 : 1
      const statusText = newStatus === 1 ? '启用' : '禁用'

      this.$confirm(`确定要${statusText}该会员吗？`, '提示', {
        type: 'warning'
      }).then(() => {
        updateUserStatus({
          id: row.id,
          status: newStatus
        }).then(() => {
          this.$message.success(`${statusText}成功`)
          this.fetchList()
        })
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 查看详情
    handleViewDetail(row) {
      this.selectedUserId = row.id
      this.showDetailDialog = true
    }
  }
}
</script>

<style scoped>
.user-page {
  padding: 20px;
}

.search-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
