<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员管理API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .api-info { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .params { margin: 10px 0; }
        .param-item { margin: 5px 0; }
        .response-example { background: #f9f9f9; padding: 10px; border-left: 4px solid #007bff; margin: 10px 0; }
        pre { background: #f8f8f8; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>会员管理模块API测试文档</h1>
    
    <div class="test-section">
        <div class="test-title">1. 会员列表查询接口</div>
        <div class="api-info">
            <strong>接口地址：</strong>/admin/api/user/list<br>
            <strong>请求方式：</strong>GET<br>
            <strong>功能描述：</strong>分页查询会员列表，支持按邮箱、手机号、昵称、状态筛选
        </div>
        
        <div class="params">
            <strong>请求参数：</strong>
            <div class="param-item">• email: 邮箱地址（可选）</div>
            <div class="param-item">• mobile: 手机号（可选）</div>
            <div class="param-item">• nickName: 昵称（可选）</div>
            <div class="param-item">• status: 状态，1-启用，0-禁用（可选）</div>
            <div class="param-item">• pageNum: 页码（可选，默认1）</div>
            <div class="param-item">• pageSize: 每页记录数（可选，默认10）</div>
        </div>
        
        <div class="response-example">
            <strong>响应示例：</strong>
            <pre>{
  "data": {
    "list": [
      {
        "id": 1,
        "nickName": "张三",
        "email": "<EMAIL>",
        "mobile": "13800138000",
        "sex": 1,
        "birthday": "1990-01-01",
        "height": 175,
        "weight": 70,
        "address": "北京市朝阳区",
        "status": 1,
        "createdTime": "2024-01-01 10:00:00",
        "avatar": "",
        "appleid": "",
        "openid": "",
        "updatedTime": "2024-01-01 10:00:00"
      }
    ],
    "pageNo": 1,
    "pageSize": 10,
    "total": 100
  },
  "errorCode": "",
  "errorMessage": "",
  "success": true
}</pre>
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">2. 会员状态更新接口</div>
        <div class="api-info">
            <strong>接口地址：</strong>/admin/api/user/updateStatus<br>
            <strong>请求方式：</strong>POST<br>
            <strong>功能描述：</strong>更新会员状态（启用/禁用）
        </div>
        
        <div class="params">
            <strong>请求参数：</strong>
            <div class="param-item">• id: 用户ID（必填）</div>
            <div class="param-item">• status: 状态，1-启用，0-禁用（必填）</div>
        </div>
        
        <div class="response-example">
            <strong>响应示例：</strong>
            <pre>{
  "data": {},
  "errorCode": "",
  "errorMessage": "",
  "success": true
}</pre>
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">3. 用户详情查询接口</div>
        <div class="api-info">
            <strong>接口地址：</strong>/admin/api/user/detail/{id}<br>
            <strong>请求方式：</strong>GET<br>
            <strong>功能描述：</strong>根据用户ID查询用户详细信息
        </div>

        <div class="params">
            <strong>请求参数：</strong>
            <div class="param-item">• id: 用户ID（路径参数，必填）</div>
        </div>

        <div class="response-example">
            <strong>响应示例：</strong>
            <pre>{
  "data": {
    "id": 1,
    "nickName": "张三",
    "email": "<EMAIL>",
    "mobile": "13800138000",
    "sex": 1,
    "birthday": "1990-01-01",
    "height": 175,
    "weight": 70,
    "address": "北京市朝阳区",
    "status": 1,
    "avatar": "http://example.com/avatar.jpg",
    "appleid": "",
    "openid": "",
    "createdTime": "2024-01-01 10:00:00",
    "updatedTime": "2024-01-01 10:00:00"
  },
  "errorCode": "",
  "errorMessage": "",
  "success": true
}</pre>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">4. 页面功能说明</div>
        <div class="api-info">
            <strong>已实现功能：</strong><br>
            • 会员列表展示（表格形式）<br>
            • 多条件搜索（邮箱、手机号、昵称、状态）<br>
            • 分页功能<br>
            • 状态切换（启用/禁用）<br>
            • 用户详情查看（点击行或详情按钮）<br>
            • 头像显示（支持默认头像）<br>
            • 响应式布局<br>
            • 加载状态提示<br>
            • 操作确认提示
        </div>
        
        <div class="params">
            <strong>页面路由：</strong>
            <div class="param-item">• 访问路径：/uservip/user</div>
            <div class="param-item">• 组件位置：src/views/uservip/user/index.vue</div>
            <div class="param-item">• 详情组件：src/views/uservip/user/components/UserDetail.vue</div>
            <div class="param-item">• API文件：src/api/user.js</div>
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">5. 测试步骤</div>
        <div class="params">
            <div class="param-item">1. 启动开发服务器：npm run dev</div>
            <div class="param-item">2. 访问：http://localhost:5175/uservip/user</div>
            <div class="param-item">3. 测试搜索功能：输入搜索条件点击搜索</div>
            <div class="param-item">4. 测试分页功能：切换页码和每页显示数量</div>
            <div class="param-item">5. 测试状态切换：点击启用/禁用按钮</div>
            <div class="param-item">6. 测试详情查看：点击表格行或"查看详情"按钮</div>
            <div class="param-item">7. 测试头像显示：检查详情弹窗中的头像是否正确显示</div>
            <div class="param-item">8. 检查网络请求：打开浏览器开发者工具查看API调用</div>
        </div>
    </div>
</body>
</html>
