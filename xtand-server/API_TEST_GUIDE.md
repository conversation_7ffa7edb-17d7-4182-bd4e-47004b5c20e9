# App登录功能API测试指南

## 功能概述

我们已经成功实现了完整的app登录功能，支持以下登录方式：
- 手机号 + 验证码登录
- 邮箱 + 验证码登录
- 微信登录
- Apple登录

## API接口

### 1. 发送验证码

**接口地址：** `POST /app/api/user/send-verification-code`

**请求参数：**
```json
{
    "type": 0,  // 0-手机号 1-邮箱
    "target": "13800138000"  // 手机号或邮箱地址
}
```

**响应示例：**
```json
{
    "success": true,
    "data": true,
    "errorCode": null,
    "errorMessage": null
}
```

**测试命令：**
```bash
# 发送手机验证码
curl -X POST http://localhost:8082/app/api/user/send-verification-code \
  -H "Content-Type: application/json" \
  -d '{"type": 0, "target": "13800138000"}'

# 发送邮箱验证码
curl -X POST http://localhost:8082/app/api/user/send-verification-code \
  -H "Content-Type: application/json" \
  -d '{"type": 1, "target": "<EMAIL>"}'
```

### 2. 用户登录

**接口地址：** `POST /app/api/user/login`

**请求参数：**

#### 手机号登录
```json
{
    "loginType": 0,
    "content": "13800138000",
    "verificationCode": "123456",
    "deviceInfo": "iPhone 15 Pro"
}
```

#### 邮箱登录
```json
{
    "loginType": 1,
    "content": "<EMAIL>",
    "verificationCode": "123456",
    "deviceInfo": "iPhone 15 Pro"
}
```

#### 微信登录
```json
{
    "loginType": 3,
    "content": "wx_auth_code",
    "encryptedData": "encrypted_data",
    "iv": "iv_string",
    "deviceInfo": "iPhone 15 Pro"
}
```

#### Apple登录
```json
{
    "loginType": 4,
    "content": "apple_auth_code",
    "deviceInfo": "iPhone 15 Pro"
}
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "token": "eyJhbGciOiJIUzUxMiJ9...",
        "refreshToken": "eyJhbGciOiJIUzUxMiJ9...",
        "expire": 1642694400000,
        "refreshExpire": 1645286400000,
        "isNewUser": true,
        "user": {
            "id": 1,
            "nickName": "用户8000",
            "mobile": "13800138000",
            "email": null,
            "status": 1
        }
    },
    "errorCode": null,
    "errorMessage": null
}
```

**测试命令：**
```bash
# 手机号登录（使用万能验证码000000）
curl -X POST http://localhost:8082/app/api/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "loginType": 0,
    "content": "13800138000",
    "verificationCode": "000000",
    "deviceInfo": "Test Device"
  }'

# 邮箱登录（使用万能验证码000000）
curl -X POST http://localhost:8082/app/api/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "loginType": 1,
    "content": "<EMAIL>",
    "verificationCode": "000000",
    "deviceInfo": "Test Device"
  }'
```

### 3. 刷新Token

**接口地址：** `POST /app/api/user/refresh-token`

**请求参数：**
```json
{
    "refreshToken": "eyJhbGciOiJIUzUxMiJ9...",
    "deviceInfo": "iPhone 15 Pro"
}
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "token": "eyJhbGciOiJIUzUxMiJ9...",
        "refreshToken": "eyJhbGciOiJIUzUxMiJ9...",
        "expire": 1642694400000,
        "refreshExpire": 1645286400000
    },
    "errorCode": null,
    "errorMessage": null
}
```

**测试命令：**
```bash
# 刷新Token（需要先登录获取refreshToken）
curl -X POST http://localhost:8082/app/api/user/refresh-token \
  -H "Content-Type: application/json" \
  -d '{
    "refreshToken": "your_refresh_token_here",
    "deviceInfo": "Test Device"
  }'
```

## 核心特性

### 1. 自动用户注册
- 首次登录的用户会自动注册到系统
- 根据登录方式设置相应的用户信息（手机号、邮箱、openid、appleid）
- 自动生成默认昵称

### 2. 验证码机制
- 支持手机短信验证码（阿里云短信服务）
- 支持邮箱验证码（HTML模板邮件）
- 验证码5分钟有效期
- 发送频率限制（1分钟内只能发送一次）
- 万能验证码"000000"用于测试

### 3. Token管理
- JWT访问令牌（2小时有效期）
- 刷新令牌（30天有效期）
- 令牌包含用户基本信息
- 支持refreshToken自动刷新机制
- 刷新时会生成新的访问令牌和刷新令牌

### 4. 第三方登录
- 微信登录支持获取用户手机号
- Apple登录预留接口
- 统一的登录响应格式

### 5. 安全特性
- 手机号和邮箱格式验证
- 用户状态检查（禁用用户无法登录）
- 验证码验证后立即删除
- 登录类型验证
- RefreshToken签名验证
- Token过期时间检查
- 用户状态实时验证

## 数据库设计

用户信息存储在`user`表中，支持多种登录方式：
- `mobile`: 手机号
- `email`: 邮箱
- `openid`: 微信openid
- `appleid`: Apple用户标识
- `status`: 用户状态（1-启用 0-禁用）
- `is_deleted`: 逻辑删除标记

## 错误处理

系统提供完善的错误处理机制：
- 参数验证错误
- 验证码相关错误（过期、错误、发送失败）
- 登录类型不支持
- 用户状态异常
- 第三方登录失败
- RefreshToken无效或过期
- 用户不存在或已被删除

## 复用的现有组件

我们充分复用了项目中的现有组件：
- `SmsService`: 阿里云短信服务
- `TemplateEmailService`: 邮件模板服务
- `RedisService`: Redis缓存服务
- `VerificationCodeGenerator`: 验证码生成器
- `JWTTokenHelper`: JWT工具类
- `WXApiUtil`: 微信API工具
- `JsonResult`: 统一响应格式
- `BusinessExceptionEnum`: 业务异常枚举

## 下一步建议

1. **完善Apple登录**: 集成Apple Sign-In SDK
2. **添加登录日志**: 记录用户登录历史
3. **增强安全性**: 添加设备指纹、IP限制等
4. **性能优化**: 添加缓存、异步处理等
5. **监控告警**: 添加登录成功率监控
