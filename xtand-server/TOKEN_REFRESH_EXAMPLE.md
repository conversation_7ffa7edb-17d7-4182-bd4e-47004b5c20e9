# Token刷新功能使用示例

## 功能说明

Token刷新功能允许客户端在访问令牌过期时，使用刷新令牌获取新的访问令牌，而无需用户重新登录。这提供了更好的用户体验和安全性。

## 工作流程

```
1. 用户登录 → 获得 accessToken (2小时) + refreshToken (30天)
2. 使用 accessToken 访问API
3. accessToken 过期 → 使用 refreshToken 刷新
4. 获得新的 accessToken + refreshToken
5. 继续使用新的 accessToken 访问API
```

## 完整示例

### 步骤1: 用户登录

```bash
curl -X POST http://localhost:8082/app/api/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "loginType": 0,
    "content": "13800138000",
    "verificationCode": "000000",
    "deviceInfo": "iPhone 15 Pro"
  }'
```

**响应:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9.eyJleHAiOjE2NDI2OTQ0MDAsInVzZXJJZCI6MSwidXNlck5hbWUiOiJ1c2VyODAwMCIsImlzU3VwZXJBZG1pbiI6MCwicm9sZSI6InVzZXIifQ.xxx",
    "refreshToken": "eyJhbGciOiJIUzUxMiJ9.eyJleHAiOjE2NDUyODY0MDAsInVzZXJJZCI6MSwidXNlck5hbWUiOiJ1c2VyODAwMCIsImlzU3VwZXJBZG1pbiI6MCwicm9sZSI6InVzZXIifQ.yyy",
    "expire": 1642694400000,
    "refreshExpire": 1645286400000,
    "isNewUser": true,
    "user": {
      "id": 1,
      "nickName": "用户8000",
      "mobile": "13800138000"
    }
  }
}
```

### 步骤2: 使用访问令牌访问API

```bash
curl -X GET http://localhost:8082/app/api/user/detail/1 \
  -H "Authorization: eyJhbGciOiJIUzUxMiJ9.eyJleHAiOjE2NDI2OTQ0MDAsInVzZXJJZCI6MSwidXNlck5hbWUiOiJ1c2VyODAwMCIsImlzU3VwZXJBZG1pbiI6MCwicm9sZSI6InVzZXIifQ.xxx"
```

### 步骤3: 访问令牌过期，使用刷新令牌

当访问令牌过期时，API会返回401错误。此时客户端应该使用refreshToken刷新令牌：

```bash
curl -X POST http://localhost:8082/app/api/user/refresh-token \
  -H "Content-Type: application/json" \
  -d '{
    "refreshToken": "eyJhbGciOiJIUzUxMiJ9.eyJleHAiOjE2NDUyODY0MDAsInVzZXJJZCI6MSwidXNlck5hbWUiOiJ1c2VyODAwMCIsImlzU3VwZXJBZG1pbiI6MCwicm9sZSI6InVzZXIifQ.yyy",
    "deviceInfo": "iPhone 15 Pro"
  }'
```

**响应:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9.eyJleHAiOjE2NDI3MDE2MDAsInVzZXJJZCI6MSwidXNlck5hbWUiOiJ1c2VyODAwMCIsImlzU3VwZXJBZG1pbiI6MCwicm9sZSI6InVzZXIifQ.zzz",
    "refreshToken": "eyJhbGciOiJIUzUxMiJ9.eyJleHAiOjE2NDUyOTM2MDAsInVzZXJJZCI6MSwidXNlck5hbWUiOiJ1c2VyODAwMCIsImlzU3VwZXJBZG1pbiI6MCwicm9sZSI6InVzZXIifQ.aaa",
    "expire": 1642701600000,
    "refreshExpire": 1645293600000
  }
}
```

### 步骤4: 使用新的访问令牌继续访问API

```bash
curl -X GET http://localhost:8082/app/api/user/detail/1 \
  -H "Authorization: eyJhbGciOiJIUzUxMiJ9.eyJleHAiOjE2NDI3MDE2MDAsInVzZXJJZCI6MSwidXNlck5hbWUiOiJ1c2VyODAwMCIsImlzU3VwZXJBZG1pbiI6MCwicm9sZSI6InVzZXIifQ.zzz"
```

## 客户端实现建议

### JavaScript/TypeScript 示例

```javascript
class TokenManager {
  constructor() {
    this.accessToken = localStorage.getItem('accessToken');
    this.refreshToken = localStorage.getItem('refreshToken');
  }

  async makeRequest(url, options = {}) {
    // 添加访问令牌到请求头
    const headers = {
      ...options.headers,
      'Authorization': this.accessToken
    };

    let response = await fetch(url, { ...options, headers });

    // 如果访问令牌过期，尝试刷新
    if (response.status === 401) {
      const refreshed = await this.refreshAccessToken();
      if (refreshed) {
        // 重新发送原始请求
        headers['Authorization'] = this.accessToken;
        response = await fetch(url, { ...options, headers });
      } else {
        // 刷新失败，跳转到登录页
        this.redirectToLogin();
        return null;
      }
    }

    return response;
  }

  async refreshAccessToken() {
    try {
      const response = await fetch('/app/api/user/refresh-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          refreshToken: this.refreshToken,
          deviceInfo: navigator.userAgent
        })
      });

      if (response.ok) {
        const data = await response.json();
        this.accessToken = data.data.token;
        this.refreshToken = data.data.refreshToken;
        
        // 保存到本地存储
        localStorage.setItem('accessToken', this.accessToken);
        localStorage.setItem('refreshToken', this.refreshToken);
        
        return true;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }
    
    return false;
  }

  redirectToLogin() {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    window.location.href = '/login';
  }
}
```

### iOS Swift 示例

```swift
class TokenManager {
    private var accessToken: String?
    private var refreshToken: String?
    
    func makeRequest(url: URL, completion: @escaping (Data?, URLResponse?, Error?) -> Void) {
        var request = URLRequest(url: url)
        request.setValue(accessToken, forHTTPHeaderField: "Authorization")
        
        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 401 {
                // Token过期，尝试刷新
                self?.refreshAccessToken { success in
                    if success {
                        // 重新发送请求
                        request.setValue(self?.accessToken, forHTTPHeaderField: "Authorization")
                        URLSession.shared.dataTask(with: request, completionHandler: completion).resume()
                    } else {
                        // 刷新失败，跳转登录
                        DispatchQueue.main.async {
                            self?.redirectToLogin()
                        }
                    }
                }
            } else {
                completion(data, response, error)
            }
        }.resume()
    }
    
    private func refreshAccessToken(completion: @escaping (Bool) -> Void) {
        guard let refreshToken = refreshToken else {
            completion(false)
            return
        }
        
        let url = URL(string: "/app/api/user/refresh-token")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let body = ["refreshToken": refreshToken, "deviceInfo": UIDevice.current.model]
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            if let data = data,
               let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let responseData = json["data"] as? [String: Any],
               let newAccessToken = responseData["token"] as? String,
               let newRefreshToken = responseData["refreshToken"] as? String {
                
                self?.accessToken = newAccessToken
                self?.refreshToken = newRefreshToken
                completion(true)
            } else {
                completion(false)
            }
        }.resume()
    }
}
```

## 安全注意事项

1. **安全存储**: RefreshToken应该安全存储，避免XSS攻击
2. **HTTPS**: 所有Token相关请求必须使用HTTPS
3. **过期处理**: 当RefreshToken也过期时，必须重新登录
4. **设备绑定**: 可以考虑将RefreshToken与设备信息绑定
5. **撤销机制**: 提供Token撤销功能，用于安全退出

## 错误处理

常见错误及处理方式：

- **RefreshToken无效**: 跳转到登录页面
- **RefreshToken过期**: 跳转到登录页面  
- **用户被禁用**: 显示账户被禁用提示
- **网络错误**: 重试机制或提示用户检查网络

## 最佳实践

1. **自动刷新**: 在Token即将过期前主动刷新
2. **并发控制**: 避免同时发起多个刷新请求
3. **失败重试**: 网络失败时的重试机制
4. **用户体验**: 刷新过程对用户透明
5. **日志记录**: 记录Token刷新的关键事件
