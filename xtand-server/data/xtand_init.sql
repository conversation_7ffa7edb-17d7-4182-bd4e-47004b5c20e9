-- MySQL dump 10.13  Distrib 5.7.34, for osx10.16 (x86_64)
--
-- Host: localhost    Database: xtand
-- ------------------------------------------------------
-- Server version	5.7.34-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `sys_dept`
--

DROP TABLE IF EXISTS `sys_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_dept` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父部门ID',
  `name` varchar(50) NOT NULL COMMENT '部门名称',
  `order_num` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态（1启用 0禁用）',
  `created_by` varchar(19) DEFAULT NULL COMMENT '创建人ID',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人ID',
  `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dept`
--

LOCK TABLES `sys_dept` WRITE;
/*!40000 ALTER TABLE `sys_dept` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_menu`
--

DROP TABLE IF EXISTS `sys_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_menu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父菜单ID',
  `name` varchar(50) NOT NULL COMMENT '菜单名称',
  `type` tinyint(4) NOT NULL COMMENT '类型（0目录 1菜单 2按钮）',
  `path` varchar(200) DEFAULT NULL COMMENT '路由路径',
  `is_system` tinyint(4) DEFAULT NULL COMMENT '是否系统菜单：1是，0否',
  `permission` varchar(100) DEFAULT NULL COMMENT '权限标识（如user:add）',
  `icon` varchar(50) DEFAULT NULL COMMENT '菜单图标',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `visible` tinyint(4) DEFAULT '1' COMMENT '是否可见（1显示 0隐藏）',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态（1启用 0禁用）',
  `created_by` varchar(19) DEFAULT NULL COMMENT '创建人ID',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人ID',
  `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COMMENT='菜单/按钮表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_menu`
--

LOCK TABLES `sys_menu` WRITE;
/*!40000 ALTER TABLE `sys_menu` DISABLE KEYS */;
INSERT INTO `sys_menu` VALUES (1,0,'系统管理',0,'/system',1,'sys','setting',1,1,1,NULL,'2025-07-12 16:03:57',NULL,'2025-07-12 16:03:57',0),(2,1,'用户管理',1,'/system/user',1,'sys:user','user',1,1,1,NULL,'2025-07-12 16:03:57',NULL,'2025-07-12 16:03:57',0),(3,2,'新增用户',2,NULL,1,'sys:user:add',NULL,1,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0),(4,2,'编辑用户',2,NULL,1,'sys:user:edit',NULL,2,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0),(5,2,'删除用户',2,NULL,1,'sys:user:delete',NULL,3,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0),(6,2,'重置密码',2,NULL,1,'sys:user:resetPwd',NULL,4,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0),(7,1,'角色管理',1,'/system/role',1,'sys:role','team',2,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0),(8,7,'新增角色',2,NULL,1,'sys:role:add',NULL,1,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0),(9,7,'编辑角色',2,NULL,1,'sys:role:edit',NULL,2,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0),(10,7,'删除角色',2,NULL,1,'sys:role:delete',NULL,3,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0),(11,7,'分配权限',2,NULL,1,'sys:role:assign',NULL,4,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0),(12,1,'菜单管理',1,'/system/menu',1,'sys:menu','menu',3,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0),(13,12,'新增菜单',2,NULL,1,'sys:menu:add',NULL,1,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0),(14,12,'编辑菜单',2,NULL,1,'sys:menu:edit',NULL,2,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0),(15,12,'删除菜单',2,NULL,1,'sys:menu:delete',NULL,3,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',0);
/*!40000 ALTER TABLE `sys_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role`
--

DROP TABLE IF EXISTS `sys_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `code` varchar(50) NOT NULL COMMENT '角色标识（如admin、user）',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态（1启用 0禁用）',
  `description` varchar(200) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(19) DEFAULT NULL COMMENT '创建人ID',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人ID',
  `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `is_system` int(11) DEFAULT NULL COMMENT '是否是系统角色 1-是 0-否',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_key` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role`
--

LOCK TABLES `sys_role` WRITE;
/*!40000 ALTER TABLE `sys_role` DISABLE KEYS */;
INSERT INTO `sys_role` VALUES (1,'管理员','admin',1,'系统管理员',NULL,'2025-07-12 16:03:56',NULL,'2025-07-12 16:03:56',0,1),(2,'业务经理','business',1,'','1','2025-07-13 10:47:53',1,'2025-07-13 10:47:53',0,0);
/*!40000 ALTER TABLE `sys_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_menu`
--

DROP TABLE IF EXISTS `sys_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_role_menu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单/按钮ID',
  `created_by` varchar(19) DEFAULT NULL COMMENT '创建人ID',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人ID',
  `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COMMENT='角色-菜单/按钮关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_menu`
--

LOCK TABLES `sys_role_menu` WRITE;
/*!40000 ALTER TABLE `sys_role_menu` DISABLE KEYS */;
INSERT INTO `sys_role_menu` VALUES (1,1,1,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(2,1,2,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(3,1,3,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(4,1,4,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(5,1,5,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(6,1,6,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(7,1,7,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(8,1,8,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(9,1,9,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(10,1,10,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(11,1,11,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(12,1,12,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(13,1,13,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(14,1,14,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(15,1,15,NULL,'2025-07-12 16:04:39',NULL,'2025-07-12 16:04:39',1),(16,2,16,'1','2025-07-13 16:34:14',1,'2025-07-13 16:34:14',1),(17,1,1,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(18,1,2,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(19,1,3,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(20,1,4,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(21,1,5,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(22,1,6,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(23,1,7,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(24,1,8,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(25,1,9,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(26,1,10,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(27,1,11,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(28,1,12,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(29,1,13,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(30,1,14,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(31,1,15,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(32,1,16,'1','2025-07-13 16:56:48',1,'2025-07-13 16:56:48',0),(33,1,17,'1','2025-07-13 17:10:20',1,'2025-07-13 17:10:20',0),(34,1,18,'1','2025-07-13 17:11:49',1,'2025-07-13 17:11:49',0);
/*!40000 ALTER TABLE `sys_role_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码（加密存储）',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态（1启用 0禁用）',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '所属部门ID',
  `is_super_admin` tinyint(4) DEFAULT '0' COMMENT '是否超级管理员（1是 0否）',
  `created_by` varchar(19) DEFAULT NULL COMMENT '创建人ID',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人ID',
  `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`user_name`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user`
--

LOCK TABLES `sys_user` WRITE;
/*!40000 ALTER TABLE `sys_user` DISABLE KEYS */;
INSERT INTO `sys_user` VALUES (1,'root','21232f297a57a5a743894a0e4a801fc3','13800000000','<EMAIL>',1,NULL,1,NULL,'2025-07-12 16:03:56',NULL,'2025-07-12 16:03:56',0),(2,'zhangsan','e10adc3949ba59abbe56e057f20f883e','15016631935','<EMAIL>',1,NULL,0,'1','2025-07-13 09:51:43',1,'2025-07-13 09:51:43',0);
UNLOCK TABLES;

--
-- Table structure for table `sys_user_role`
--

DROP TABLE IF EXISTS `sys_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_user_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `created_by` varchar(19) DEFAULT NULL COMMENT '创建人ID',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人ID',
  `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='用户-角色关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_role`
--

LOCK TABLES `sys_user_role` WRITE;
/*!40000 ALTER TABLE `sys_user_role` DISABLE KEYS */;
INSERT INTO `sys_user_role` VALUES (1,2,1,NULL,'2025-07-12 16:03:56',NULL,'2025-07-12 16:03:56',0);
/*!40000 ALTER TABLE `sys_user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'xtand'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-14 15:24:55
