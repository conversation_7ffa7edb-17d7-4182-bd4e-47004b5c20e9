package com.mebotx.xtand;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @Date 2025/3/30 22:59
 * @Desc
 */
@SpringBootApplication
@MapperScan(basePackages =
        {
        "com.mebotx.xtand.modules.system.mapper", "com.mebotx.xtand.modules.uservip.mapper"
})
@EnableScheduling
@EnableAsync
public class XtandApplication {

    public static void main(String[] args) {
        SpringApplication.run(XtandApplication.class, args);
    }

}
