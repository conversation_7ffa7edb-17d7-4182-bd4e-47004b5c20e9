package com.mebotx.xtand.common.config;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.mebotx.xtand.common.handler.CommonFiledHandler;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * @version 1.0.0
 * @description
 * @date 2025/3/30
 * 1、配置类里面使用@Bean标注在方法上个容器注册组件，默认也是单实例的
 * 2、配置类本身也是组件
 * 3、proxyBeanMethods：代理bean的方法
 *      Full(proxyBeanMethods = true)
 *      List(proxyBeanMethods = false)
 *      组件依赖
 */
@Configuration(proxyBeanMethods = false)
@AutoConfigureAfter(MybatisPlusAutoConfiguration.class)
public class MybatisPlusConfig {

    /**
     * 新的分页插件，一缓和二缓遵循mybatis的规则，需要设置MybatisConfiguration#useDeprecatedExecutor = false
     * 避免缓存出现问题（该属性会在旧插件移除后一同移除）
     */

    @ConditionalOnMissingBean
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor(){
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 分页插件
        interceptor.addInnerInterceptor(paginationInnerInterceptor());
        return interceptor;
    }

    /**
     * 分页插件，自动识别数据库类型
     */
    public PaginationInnerInterceptor paginationInnerInterceptor() {
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInnerInterceptor.setMaxLimit(-1L);
        // 设置请求的页面大于最大页后操作， true 调回到首页，false 继续请求默认false
        paginationInnerInterceptor.setOverflow(true);
        return paginationInnerInterceptor;
    }

//    @ConditionalOnMissingBean
    @Bean
    @Primary
    public MetaObjectHandler metaObjectHandler(){
        return new CommonFiledHandler();
    }

}
