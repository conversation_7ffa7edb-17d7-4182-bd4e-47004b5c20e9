package com.mebotx.xtand.common.config;

import com.aliyun.dysmsapi20170525.Client;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.HashMap;
import java.util.Map;
import com.aliyun.teaopenapi.models.Config;

@Slf4j
@Configuration
public class SMSconfig {


    @Bean
    public Client smsClient() {

        try {
            Map<String, String> map = new HashMap<>();
            map.put("accessKeyId", "LTAI5tCXbg9FeXSufDZYa6ns");
            map.put("accessKeySecret", "******************************");
            map.put("STSEndpoint", "dysmsapi.aliyuncs.com");
            Config config = Config.build(map);

            return new Client(config);
        }catch (Exception ex){
            log.error("smsClient",ex);
            return null;
        }
    }

}
