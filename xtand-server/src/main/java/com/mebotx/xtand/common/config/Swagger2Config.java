package com.mebotx.xtand.common.config;


import com.mebotx.xtand.common.properties.SwaggerProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/31 19:29
 * @Desc
 */
@Configuration
@EnableConfigurationProperties(SwaggerProperties.class)
@EnableSwagger2
public class Swagger2Config {

    @Resource
    private SwaggerProperties swaggerProperties;


    /**
     * 配置swagger2核心配置
     */
    @Bean
    public Docket createRestApi(){

        // 每个请求默认所带的参数
        List<Parameter> parameters = new ArrayList<>();
        parameters.add(new ParameterBuilder()
                .name("Authorization")
                .description("访问令牌")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .defaultValue("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJleHAiOjE3MTE0MzkzNjQsInVzZXJOYW1lIjoiMTg3NzM2MTEzMzYiLCJ1c2VySWQiOjE4OTAwfQ.mLFSgdgMljU0VUqVB3aPwLlSI9WLfAag8E0DJUvt_povHbJmo7GMzBWnjgoDBqLTG1vd1ydlnNjFDviNU35-xg")
                .required(false)
                .build());

        return new Docket(DocumentationType.SWAGGER_2)
                .enable(swaggerProperties.getEnable())
                .apiInfo(apiInfo("2.0"))
                .globalOperationParameters(parameters)
                .select()
                .apis(RequestHandlerSelectors.basePackage(swaggerProperties.getScanBasePackage()))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo(String version){
        return new ApiInfoBuilder()
                .title(swaggerProperties.getTitle())
                .description(swaggerProperties.getDescription())
                .version(version)
                .build();
    }
}
