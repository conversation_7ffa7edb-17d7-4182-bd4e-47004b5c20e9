package com.mebotx.xtand.common.config;


import com.mebotx.xtand.common.properties.ThreadPoolProperties;
import com.mebotx.xtand.utils.ThreadUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @Date 2025/3/31 10:25
 * @Desc
 */
@Slf4j
@EnableAsync
@Configuration
public class ThreadPoolConfig implements AsyncConfigurer {

    /**
     * 线程池配置
     */
    @Resource
    private ThreadPoolProperties threadPoolProperties;

    @Override
    @Bean("asyncExecutor")
    public ExecutorService getAsyncExecutor() {
        BlockingQueue<Runnable> blockingQueue = new LinkedBlockingQueue<>(threadPoolProperties.getQueueCapacity());
        return new ThreadPoolExecutor(threadPoolProperties.getCorePoolSize(), threadPoolProperties.getMaxPoolSize(), 300, TimeUnit.SECONDS, blockingQueue, new ThreadFactory() {
            private final AtomicInteger poolNumber = new AtomicInteger(1);
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            @Override
            public Thread newThread(Runnable r) {
                SecurityManager securityManager = System.getSecurityManager();
                Thread t = new Thread(securityManager != null ? securityManager.getThreadGroup() : Thread.currentThread().getThreadGroup(), r,
                        "asyncExecutor-" + poolNumber.getAndIncrement() + "-thread-" + threadNumber.getAndIncrement(), 0);
                if (t.isDaemon()) {
                    t.setDaemon(false);
                }
                if (t.getPriority() != Thread.NORM_PRIORITY) {
                    t.setPriority(Thread.NORM_PRIORITY);
                }
                return t;
            }
        }, (r, executor) -> r.run());
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new AsyncExceptionHandler();
    }

    private static class AsyncExceptionHandler implements AsyncUncaughtExceptionHandler{

        @Override
        public void handleUncaughtException(Throwable throwable, Method method, Object... objects) {
            log.error("异常原因是：" + throwable.getMessage(), "所调用的方法是：" + method.getName(), "参数是：" + Arrays.toString(objects));
        }
    }

    /**
     * 执行周期性或定时任务
     */
    @Bean(name = "scheduledExecutorService")
    protected ScheduledExecutorService scheduledExecutorService() {
        return new ScheduledThreadPoolExecutor(threadPoolProperties.getCorePoolSize(),
                new BasicThreadFactory.Builder().namingPattern("schedule-pool-%d").daemon(true).build(),
                new ThreadPoolExecutor.CallerRunsPolicy()) {
            @Override
            protected void afterExecute(Runnable r, Throwable t) {
                super.afterExecute(r, t);
                ThreadUtils.printException(r, t);
            }
        };
    }

}
