package com.mebotx.xtand.common.constant;


/**
 * 申请单类型
 */
public enum ApprovalApplyTypeEnum {


     PURCHASE_IN("P1", "采购-入场申请"),

    AFTER_SALE_IN("A1", "售后-入场申请"),

    AFTER_SALE_OUT("A0", "售后-出场申请"),

    OUTSOURCING_IN("S1", "委外-入场申请"),

    OUTSOURCING_OUT("S0", "委外-出场申请"),

    ;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private String code;

    private String desc;


    ApprovalApplyTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(String code){
        for(ApprovalApplyTypeEnum e:  ApprovalApplyTypeEnum.values()){
           if(e.getCode().equals(code)){
               return e.getDesc();
            }

        }
        return null;

    }

}
