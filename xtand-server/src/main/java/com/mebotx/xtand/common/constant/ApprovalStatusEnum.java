package com.mebotx.xtand.common.constant;

/**
 * 申请单状态
 */

public enum ApprovalStatusEnum {

    //'draft','pending','approved','rejected'
    DRAFT("draft","草稿"),
    PENDING("pending","审批中"),
    APPROVED("approved","通过"),
    REJECTED("rejected","驳回"),
    ;

    public String getDesc() {
        return desc;
    }

    String code;

    public String getCode() {
        return code;
    }

    String desc;


    ApprovalStatusEnum(String code,String desc){
        this.code=code;
        this.desc=desc;
    }
    public static String getDesc(String code){
        for(ApprovalStatusEnum e:  ApprovalStatusEnum.values()){
            if(e.getCode().equals(code)){
                return e.getDesc();
            }

        }
        return null;

    }

}
