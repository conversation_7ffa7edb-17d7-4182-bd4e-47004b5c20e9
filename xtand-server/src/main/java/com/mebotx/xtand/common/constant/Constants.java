package com.mebotx.xtand.common.constant;

/**
 * @version 1.0.0
 * @description
 * @date 2024/3/21
 */
public class Constants {


    // 1:已接受、2:已拒绝、3:待处理
    public static final int FRIEND_ACCEPTED = 1;
    public static final int FRIEND_REJECTED = 2;
    public static final int FRIEND_PENDING_PROCESSING = 3;


    public static final String ADMIN_USER="admin";


    public static final String ADMIN_ROLE="admin";


    public static final String APPROVAL_ROLE="approver";



    public static final String APPLYER_ROLE="applyer";


    public static final String DOORKEEPER_ROLE="doorkeeper";

    public static final String FOLLOWER="follower";


    /**
     * 正则校验
     */
    /**
     * 正则表达式：验证手机号
     */
    public static final String REGEX_MOBILE = "^1(3[0-9]|4[01456879]|5[0-3,5-9]|6[2567]|7[0-8]|8[0-9]|9[0-3,5-9])\\d{8}$";
    /**
     * 正则表达式：验证邮箱
     */
    public static final String REGEX_EMAIL = "^([a-z0-9A-Z]+[-|_|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";


    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * www主域
     */
    public static final String WWW = "www.";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 默认时区
     */
    public static final String DEFAULT_TIME_ZONE = "GMT+8";

    /**
     * 统一的日期时间格式
     */
    public static final String DATE_TIME_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 统一的日期格式
     */
    public static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd";

    /**
     * 统一的时间格式
     */
    public static final String TIME_FORMAT_PATTERN = "HH:mm:ss";

    /**
     * 格林威治时间格式
     */
    public static final String GMT_FORMAT_PATTERN = "EEE MMM dd HH:mm:ss z yyyy";

    /**
     * traceId
     */
    public static final String TRACE_ID = "trace_id";


    /**
     * 默认等待锁时间
     */
    public static final Integer DEFAULT_WAIT_SECONDS = 3;


    /**
     * 当前登录用户
     */
    public static final String HEADER_SESSION = "X-Session";


    /***
     * 请求的Token
     */
    public static final String HEADER_TOKEN = "Authorization";


    /**
     * 请求的orderNo
     */
    public static final String HEADER_ORDER_NO = "orderNo";

    /**
     * 消息消费最大重试次数
     */
    public static final int DEFAULT_MAX_RECONSUME_TIMES = 16;

    /**
     * redis create orderNo
     */
    /**
     * redis
     */
    public static final String KEY_PREFIX = "order:";


    /**
     * 默认时区offsetId
     */
    public static final String DEFAULT_OFFSET_ID = "+8";




    public static final String APP_TOKEN_USER_KEY="app-user";

    public static final String WEB_TOKEN_USER_KEY = "admin-user";

    /**
     * 认证短信验证码的缓存key
     */
    public static final String SMS_VALIDATE_PREFIX_KEY="validate-code:1:";

    public static final String APPLY_SEQ_KEY = "apply-no";


    public static final String PENDING_APPROVAL_LIST="0";


    public static final String login_type="";

    public static final String APPROVED_LIST="1";


    public static final String ADMIN_API_GROUP="【运营端】";

    public static final String APP_API_GROUP="【APP端】";


    /**
     * 小程序
     */
    public static final String CLIENT_APP="app";

    /**
     * 运营后台
     */
    public static final String CLIENT_WEB="web";

    /**
     * 默认密码
     */
    public static final String  DEFAULT_PASSWORD="123456";

    /**
     * 是否是管理员的用户标识
     */
    public static final Integer IS_SUPER_ADMIN_FLAG=1;







}
