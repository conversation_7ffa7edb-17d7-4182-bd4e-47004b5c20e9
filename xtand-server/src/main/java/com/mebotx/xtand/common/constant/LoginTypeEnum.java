package com.mebotx.xtand.common.constant;



public enum LoginTypeEnum {



    LOGIN("login" ,"登陆"),

    LOGOUT("logout", "登出"),

            ;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private String code;

    private String desc;


    LoginTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(String code){
        for(LoginTypeEnum e:  LoginTypeEnum.values()){
            if(e.getCode().equals(code)){
                return e.getDesc();
            }

        }
        return null;

    }
}
