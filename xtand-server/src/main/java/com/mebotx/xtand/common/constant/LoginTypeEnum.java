package com.mebotx.xtand.common.constant;



public enum LoginTypeEnum {



    LOGIN_WITH_MOBILE("M" ,"手机登陆"),

    LOGIN_WITH_EMAIL("E" ,"邮箱登陆"),

    LOGiN_WITH_WECHAT("W" ,"微信登陆"),

    LOGIN_WITH_APPLE("A" ,"苹果登陆"),



            ;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private String code;

    private String desc;


    LoginTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(String code){
        for(LoginTypeEnum e:  LoginTypeEnum.values()){
            if(e.getCode().equals(code)){
                return e.getDesc();
            }

        }
        return null;

    }


    public static LoginTypeEnum getByCode(String code){
        for(LoginTypeEnum e:  LoginTypeEnum.values()){
            if(e.getCode().equals(code)){
                return e;
            }

        }
        return null;

    }
}
