package com.mebotx.xtand.common.constant;


import java.util.ArrayList;
import java.util.List;

public enum RoleEnum {



    ADMIN_ROLE(Constants.ADMIN_ROLE, "管理员"),

    APPROVAL_ROLE(Constants.APPROVAL_ROLE, "审批人"),

    APPLYER_ROLE(Constants.APPLYER_ROLE, "申请人"),

    DOORKEEPER_ROLE(Constants.DOORKEEPER_ROLE, "门卫"),

    FOLLOWER_ROLE(Constants.FOLLOWER, "关注人"),

            ;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private String code;

    private String desc;


    RoleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(String code){
        for(RoleEnum e:  RoleEnum.values()){
            if(e.getCode().equals(code)){
                return e.getDesc();
            }

        }
        return null;

    }

    public static List<String> getDescList(List<String> codes){
        if(codes==null || codes.isEmpty()){
            return null;
        }
        List<String> descList=new ArrayList<>();
        codes.forEach(e->{
          String desc=  getDesc(e);
              descList.add(desc);
             }
        );
        return descList;

    }
}
