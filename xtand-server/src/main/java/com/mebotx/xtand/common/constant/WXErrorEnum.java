package com.mebotx.xtand.common.constant;

import com.mebotx.xtand.common.exception.BaseErrorCodeEnum;


public enum WXErrorEnum implements BaseErrorCodeEnum {



    //        错误码	错误描述	解决方案
    //        40029	code 无效	js_code无效
   //        45011	api minute-quota reach limit  mustslower  retry next minute	API 调用太频繁，请稍候再试
  //        40226	code blocked	高风险等级用户，小程序登录拦截 。风险等级详见用户安全解方案
  //        -1	system error



    SYSTEM_ERROR("-1","系统繁忙，此时请开发者稍候再试"),

    JS_CODE_ERROR("40029","js_code无效"),


    TO_MANY_REQ_ERROR("45011","调用太频繁，请稍候再试"),

    HIGH_RISK_LEVEL_USERS("40226","高风险等级用户，小程序登录拦截 。风险等级详见用户安全解方案"),


        ;

    private final String errorCode;

    private final String errorMsg;

   WXErrorEnum(String code, String desc){
        this.errorCode=code;
        this.errorMsg=desc;
    }


    public static WXErrorEnum   getByCode(String code) {

        for (WXErrorEnum we : WXErrorEnum.values()) {
            if (we.getErrorCode().equals(code)) {
                return we;
            }
        }
        return null;
    }
    @Override
    public String getErrorCode() {
       return  this.errorCode;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }



}
