package com.mebotx.xtand.common.exception;

/**
 * <AUTHOR>
 * @Date 2025/3/30 23:59
 * @Desc
 */
public enum BusinessExceptionEnum implements BaseErrorCodeEnum {


    PARAM_ERROR("1000", "参数错误！{0}"),


    MOBILE_ALREADY_EXISTS("1002", "手机号已注册本系统，请更换手机号再注册"),
    USER_INFO_ABSENT("1004", "该用户不存在,请联系管理员先添加用户！"),
    OUTER_USER_FORBID("1005", "非内部用户不能使用"),

    PASSWORD_ERROR("1006", "密码错误，请重试！"),
    UPDATE_PASSWORD_ERROR("1008", "修改密码失败，旧密码错误"),
    SAME_OLD_AND_NEW_PASSWORDS("1010", "新密码不能与旧密码相同"),
    SYS_USER_EXIST("1016", "用户名已存在，请更换用户名"),

    USER_INFO_DISABLE("1018", "本账户已被禁用，请联系后台管理员解封！"),
    PAGE_NUM_OR_PAGE_SIZE_CANNOT_EMPTY("1020", "页数或页大小不能为空"),

    SUPPER_ADMIN_CANNOT_DELETE("1022", "超级管理员不允许被删除！"),

    FORBID_UPDATE_OTHER_STATUS("1024", "非管理员不能修改他人状态！"),

    USER_CAN_ONLY_UPDATE_OWN_PASSWORD("1028", "用户只能修改自己的密码!"),

    FORBID_RESET_OTHER_PASSWORD("1030", "非管理员禁止重置他人密码！"),


    SEND_SMS_FAIL("1034","发送短信失败"),

    SMS_VALIDATE_CODE_EXPIRED("1036","验证码已过期或者不存在"),

    SMS_VALIDATE_CODE_ERROR("1038","验证码不正确,请重新输入"),

    UPLOAD_FILE_ERROR("1040","上传文件失败" ),


    WORK_FLOW_EXISTS("1042","存在同名流程请确认" ),


    WORK_FLOW_NOT_EXISTS("1044","申请单关联的流程已经不存在" ),
    ;



    private final String errorCode;

    private final String errorMsg;

    BusinessExceptionEnum(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getErrorMsg() {
        return errorMsg;
    }
}
