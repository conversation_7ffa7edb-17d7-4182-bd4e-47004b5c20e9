package com.mebotx.xtand.common.exception;

/**
 * <AUTHOR>
 * @Date 2025/3/30 23:59
 * @Desc
 */
public enum CommonErrorCodeEnum implements BaseErrorCodeEnum {

    /* ====================== 系统级别未知错误 ====================== */

    /**
     * 系统未知错误
     */
    SYSTEM_UNKNOWN_ERROR("-1", "系统未知错误"),


    /* ====================== 客户端异常 ====================== */

    /**
     * 客户端HTTP请求方法错误
     * org.springframework.web.HttpRequestMethodNotSupportedException
     */
    CLIENT_HTTP_METHOD_ERROR("3001", "客户端HTTP请求方法错误"),

    /**
     * 客户端request body参数错误
     * 主要是未能通过Hibernate Validator校验的异常处理
     * org.springframework.web.bind.MethodArgumentNotValidException
     */
    CLIENT_REQUEST_BODY_CHECK_ERROR("3002", "客户端请求体参数校验不通过"),

    /**
     * 客户端@RequestBody请求体JSON格式错误或字段类型错误
     * org.springframework.http.converter.HttpMessageNotReadableException
     * eg:
     * 1、参数类型不对:{"test":"abc"}，本身类型是Long
     * 2、{"test":}  test属性没有给值
     */
    CLIENT_REQUEST_BODY_FORMAT_ERROR("3003", "客户端请求体JSON格式错误或字段类型不匹配"),

    /**
     * 客户端@PathVariable参数错误
     * 一般是类型不匹配，比如本来是Long类型，客户端却给了一个无法转换成Long字符串
     * org.springframework.validation.BindException
     */
    CLIENT_PATH_VARIABLE_ERROR("3004", "客户端URL中的参数类型错误"),

    /**
     * 客户端@RequestParam参数校验不通过
     * 主要是未能通过Hibernate Validator校验的异常处理
     * javax.validation.ConstraintViolationException
     */
    CLIENT_REQUEST_PARAM_CHECK_ERROR("3005", "客户端请求参数校验不通过"),

    /**
     * 客户端@RequestParam参数必填
     * 入参中的@RequestParam注解设置了必填，但是客户端没有给值
     * javax.validation.ConstraintViolationException
     */
    CLIENT_REQUEST_PARAM_REQUIRED_ERROR("3006", "客户端请求缺少必填的参数"),

    /**
     * 客户端请求验签失败
     * javax.validation.ValidateException
     */
    CLIENT_REQUEST_VALIDATE_ERROR("3007", "验证失败"),

    /**
     * 访问未授权
     */
    CLIENT_UNAUTHORIZED("3008", "访问未授权"),


    /* ====================== 服务端异常 ====================== */

    /**
     * 通用的业务方法入参检查错误
     * java.lang.IllegalArgumentException
     */

    TOKEN_NOT_EXISTS("3010", "访问令牌不存在，禁止访问"),

    CLIENT_REQUEST_ERROR("3020", "客户端非法请求，请确携带令牌访问"),

    TOKEN_NOT_VALID("3030", "令牌非法，禁止访问"),

    TOKEN_HAS_EXPIRED("3040", "token has expired, access is prohibited");

    private final String errorCode;

    private final String errorMsg;

    CommonErrorCodeEnum(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    @Override
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String getErrorMsg() {
        return errorMsg;
    }
}
