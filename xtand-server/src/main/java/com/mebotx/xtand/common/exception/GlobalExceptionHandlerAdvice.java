package com.mebotx.xtand.common.exception;


import com.mebotx.xtand.common.vo.resp.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/3/31 09:57
 * @Desc
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandlerAdvice {

    /**
     * 自定义异常处理
     *
     * @param request 请求
     * @param biz     异常
     * @return
     */
    @ExceptionHandler(BaseBizException.class)
    @ResponseBody
    public JsonResult<CommonErrorCodeEnum> baseCustomizeExceptionHandler(HttpServletRequest request, BaseBizException biz) {

        String url = request.getRequestURI();

        log.error("异常接口【{}】, 捕获BaseBizException异常:", url, biz);
        return JsonResult.buildError(biz.getErrorCode(), biz.getErrorMsg());
    }

    /**
     * 全局参数校验异常处理
     *
     * @param request
     * @param methodArgumentNotValidException
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public JsonResult<String> methodArgumentNotValidExceptionHandler(HttpServletRequest request, MethodArgumentNotValidException methodArgumentNotValidException) {
        String url = request.getRequestURI();
        log.error("异常接口【{}】, 捕获MethodArgumentNotValidException异常:", url, methodArgumentNotValidException);
        return JsonResult.buildError(Objects.requireNonNull(methodArgumentNotValidException.getBindingResult().getFieldError()).getDefaultMessage());
    }

    /**
     * 全局系统异常处理
     *
     * @param request 请求
     * @param e       异常
     * @return 统一返回值
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public JsonResult<CommonErrorCodeEnum> exceptionHandler(HttpServletRequest request, Exception e) {

        String url = request.getRequestURI();
        log.error("异常接口【{}】, 捕获Exception异常:", url, e);
        return JsonResult.buildError(CommonErrorCodeEnum.SYSTEM_UNKNOWN_ERROR);
    }

}
