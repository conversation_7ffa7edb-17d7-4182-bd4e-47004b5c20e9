package com.mebotx.xtand.common.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.mebotx.xtand.common.CommonEntity;
import com.mebotx.xtand.utils.StringUtil;
import com.mebotx.xtand.web.RequestHeaderHolder;
import com.mebotx.xtand.web.SessionUser;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.util.ClassUtils;

import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/3/31 10:25
 * @Desc
 */
public class CommonFiledHandler implements MetaObjectHandler {


    @Override
    public void insertFill(MetaObject metaObject) {
        if (metaObject.getOriginalObject() instanceof CommonEntity) {
            fillValIfNullByName("createdBy", getCurrUserId(), metaObject);
            fillValIfNullByName("createdTime", LocalDateTime.now(), metaObject);
            fillValIfNullByName("updatedTime", LocalDateTime.now(), metaObject);
            fillValIfNullByName("updatedBy", getCurrUserId() , metaObject);
            fillValIfNullByName("isDeleted", 0, metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if (metaObject.getOriginalObject() instanceof CommonEntity) {
            fillValIfNullByName("updateBy", getCurrUserId(), metaObject);
            fillValIfNullByName("updatedTime", new Date(), metaObject);
        }
    }

    /**
     * 填充值，先判断是否有手动设置，优先手动设置的值，例如：job必须手动设置
     *
     * @param fieldName  属性名
     * @param fieldVal   属性值
     * @param metaObject MetaObject
     */
    private static void fillValIfNullByName(String fieldName, Object fieldVal, MetaObject metaObject) {
        // 1. 没有 get 方法
        if (!metaObject.hasSetter(fieldName)) {
            return;
        }
        // 2. 如果用户有手动设置的值
        Object userSetValue = metaObject.getValue(fieldName);
        String setValueStr = StringUtil.str(userSetValue, Charset.defaultCharset());
        if (StringUtils.isNotBlank(setValueStr)) {
            return;
        }
        // 3. field 类型相同时设置
        Class<?> getterType = metaObject.getGetterType(fieldName);
        if (ClassUtils.isAssignableValue(getterType, fieldVal)) {
            metaObject.setValue(fieldName, fieldVal);
        }
    }


    private String getCurrUserId() {
        SessionUser sessionUser = RequestHeaderHolder.getSessionUser();
        return sessionUser != null ? sessionUser.getUserId() : "0";
    }
}
