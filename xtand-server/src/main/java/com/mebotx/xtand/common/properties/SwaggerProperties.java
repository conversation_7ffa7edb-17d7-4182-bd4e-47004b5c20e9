package com.mebotx.xtand.common.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "swagger")
public class SwaggerProperties {

    /**
     * 需要扫描类的package全路径，用于生成swagger文档
     */
    private String scanBasePackage;

    /**
     * swagger的标题
     */
    private String title;

    /**
     * swagger描述
     */
    private String description;

    /**
     * 是否开启swagger文档
     */
    private Boolean enable;

    public String getScanBasePackage() {
        return scanBasePackage;
    }

    public void setScanBasePackage(String scanBasePackage) {
        this.scanBasePackage = scanBasePackage;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}
