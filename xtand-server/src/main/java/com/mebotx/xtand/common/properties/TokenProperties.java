package com.mebotx.xtand.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * @version 1.0.0
 * @description
 * @date 2024/3/21
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "token")
public class TokenProperties {

    private static Long tokenExpireTime;
    private static String tokenSecret;
    private static String tokenIssuer;
    private Long expireTime;
    private String secret;
    private String issuer;

    public static Long getTokenExpireTime() {
        return tokenExpireTime;
    }

    public static String getTokenSecret() {
        return tokenSecret;
    }

    public static String getTokenIssuer() {
        return tokenIssuer;
    }

    /**
     * 初始化，赋值，将配置文件的变量读到静态变量
     */
    @PostConstruct
    public void init() {
        TokenProperties.tokenExpireTime = this.expireTime;
        TokenProperties.tokenSecret = secret;
        TokenProperties.tokenIssuer = issuer;
    }

}
