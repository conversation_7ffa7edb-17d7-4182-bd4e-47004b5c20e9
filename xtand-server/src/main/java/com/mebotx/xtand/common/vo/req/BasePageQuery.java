package com.mebotx.xtand.common.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/3/31 7:55
 * @Desc 基础分页查询对象
 */
@Data
@ApiModel
public class BasePageQuery implements Serializable {

    @ApiModelProperty(value = "页码", example = "1")
    @Min(value = 1)
    private Integer pageNum = 1;

    @Min(value = 1)
    @ApiModelProperty(value = "每页记录数", example = "10")
    private Integer pageSize = 10;
}

