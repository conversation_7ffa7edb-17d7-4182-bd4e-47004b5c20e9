package com.mebotx.xtand.common.vo.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/31 7:55
 * @Desc
 */
@Data
public class PagingInfo<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    private int pageNo = 1;

    /**
     * 每页条数
     */
    private int pageSize = 10;

    /**
     * 总条数
     */
    private Long total;

    /**
     * 数据集合
     */
    private List<T> list;

    public PagingInfo() {

    }

    public PagingInfo(Long total, List<T> list) {
        this.total = total;
        this.list = list;
    }

    public static <T> PagingInfo<T> toResponse(List<T> data, Long total, Integer currentPageNo, Integer currentPageSize) {
        PagingInfo<T> pagingObj = new PagingInfo<>();
        pagingObj.setTotal(total);
        pagingObj.setList(data);
        pagingObj.setPageNo(currentPageNo);
        pagingObj.setPageSize(currentPageSize);
        return pagingObj;
    }
}
