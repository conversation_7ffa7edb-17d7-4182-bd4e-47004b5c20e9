package com.mebotx.xtand.middleware.redis;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
/**
 * <AUTHOR>
 * @Date 2025/3/31 23:31
 * @Desc Redis 通用操作接口
 */
public interface RedisService {

    // -------------------- Key 操作 --------------------

    <PERSON><PERSON><PERSON> hasKey(String key);

    Boolean expire(String key, long timeout, TimeUnit unit);

    Boolean delete(String key);

    Long delete(List<String> keys);

    // -------------------- String 操作 --------------------

    void set(String key, String value);

    void set(String key, String value, long timeout, TimeUnit unit);

    String get(String key);

    /**
     * 自增
     * @param key 键
     * @return    值
     */
    Long incr(String key);

    Long incrBy(String key, long delta);

    /**
     * 设置键值对（仅当键不存在时）即SetNx
     * @param key 键
     * @param value 值
     * @return true-设置成功, false-键已存在
     */
    Boolean setNx(String key, String value);

    /**
     * 设置键值对（仅当键不存在时）并设置过期时间
     * @param key           键
     * @param value         值
     * @param expireTime    单位秒
     * @return
     */
    Boolean setNx(String key, String value, int expireTime);

    // -------------------- Hash 操作 --------------------

    void hSet(String key, String hashKey, String value);

    String hGet(String key, String hashKey);

    Map<Object, Object> hGetAll(String key);

    Boolean hHasKey(String key, String hashKey);

    Long hDelete(String key, String... hashKeys);

    /**
     * 检查哈希表中字段是否存在
     * @param key 哈希表键
     * @param field 字段名
     * @return true-存在, false-不存在
     */
    Boolean existsHashKey(String key, String field);

    // -------------------- List 操作 --------------------

    Long lPush(String key, String value);

    String lPop(String key);

    Long rPush(String key, String value);

    String rPop(String key);

    List<String> lRange(String key, long start, long end);

    // -------------------- Set 操作 --------------------

    Long sAdd(String key, String... values);

    Set<String> sMembers(String key);

    Boolean sIsMember(String key, String value);

    Long sRemove(String key, String... values);

    // -------------------- ZSet 操作 --------------------

    Boolean zAdd(String key, String value, double score);

    Set<String> zRange(String key, long start, long end);

    // -------------------- Lua 脚本 --------------------

    <T> T executeScript(String script, Class<T> resultType, List<String> keys, List<String> args);

    /**
     *
     * <p>Description: 尝试获取分布式锁,支持锁重入</p>
     * @param lockKey 锁
     * @param requestId 请求标识
     * @param expireTime 过期时间
     * @return true-成功 false-失败
     */
    boolean tryGetDistributedLock(String lockKey, String requestId, int expireTime);

    /**
     *
     * <p> Description: 释放分布式锁</p>
     * @param lockKey 锁
     * @param requestId 请求标识
     * @return true-成功 false-失败
     */
    boolean releaseDistributedLock(String lockKey, String requestId);
}
