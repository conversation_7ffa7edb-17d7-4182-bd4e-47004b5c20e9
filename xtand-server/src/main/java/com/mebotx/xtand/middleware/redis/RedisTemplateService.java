package com.mebotx.xtand.middleware.redis;


import com.mebotx.xtand.web.config.WebMvcConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
/**
 * <AUTHOR>
 * @Date 2025/3/31 23:37
 * @Desc
 */
@Component
public class RedisTemplateService implements RedisService {

    public static final Logger logger = LoggerFactory.getLogger(RedisTemplateService.class);

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    public static final String TRY_LOCK_SCRIPT =
            "local lockKey = KEYS[1]\n" +
                    "local requestId = ARGV[1]\n" +
                    "local expireTime = tonumber(ARGV[2])\n" +
                    "if redis.call('EXISTS',lockKey) == 0 or redis.call('HEXISTS',lockKey,requestId) == 1 then\n" +
                    "    redis.call('HINCRBY',lockKey,requestId,1)\n" +
                    "    redis.call('EXPIRE',lockKey,expireTime)\n" +
                    "    return 1\n" +
                    "else \n" +
                    "    return 0\n" +
                    "end";
    public static final String RELEASE_LOCK_SCRIPT =
            "local lockKey = KEYS[1]\n" +
                    "local requestId = ARGV[1]\n" +
                    "if redis.call('HEXISTS',lockKey,requestId) == 0 then\n" +
                    "    return nil \n" +
                    "elseif redis.call('HINCRBY',lockKey,requestId,-1) == 0 then\n" +
                    "    return redis.call('DEL',lockKey)\n" +
                    "else\n" +
                    "    return 0\n" +
                    "end";
    public static final String RENEW_SCRIPT =
            "if redis.call('HEXISTS', KEYS[1], ARGV[1]) == 1 then\n" +
                    "    return redis.call('EXPIRE', KEYS[1], ARGV[2])\n" +
                    "else\n" +
                    "    return 0\n" +
                    "end";

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    // -------------------- Key 操作 --------------------
    @Override
    public Boolean hasKey(String key) {
        return stringRedisTemplate.hasKey(key);
    }

    @Override
    public Boolean expire(String key, long timeout, TimeUnit unit) {
        return stringRedisTemplate.expire(key, timeout, unit);
    }

    @Override
    public Boolean delete(String key) {
        return stringRedisTemplate.delete(key);
    }

    @Override
    public Long delete(List<String> keys) {
        return stringRedisTemplate.delete(keys);
    }

    // -------------------- String 操作 --------------------
    @Override
    public void set(String key, String value) {
        stringRedisTemplate.opsForValue().set(key, value);
    }

    @Override
    public void set(String key, String value, long timeout, TimeUnit unit) {
        stringRedisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    @Override
    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    @Override
    public Long incr(String key) {
        return incrBy(key, 1L);
    }

    @Override
    public Long incrBy(String key, long delta) {
        return stringRedisTemplate.opsForValue().increment(key, delta);
    }

    @Override
    public Boolean setNx(String key, String value) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, value);
    }

    @Override
    public Boolean setNx(String key, String value, int expireTime) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, value, expireTime, TimeUnit.SECONDS);
    }

    // -------------------- Hash 操作 --------------------
    @Override
    public void hSet(String key, String hashKey, String value) {
        stringRedisTemplate.opsForHash().put(key, hashKey, value);
    }

    @Override
    public String hGet(String key, String hashKey) {
        return (String) stringRedisTemplate.opsForHash().get(key, hashKey);
    }

    @Override
    public Map<Object, Object> hGetAll(String key) {
        return stringRedisTemplate.opsForHash().entries(key);
    }

    @Override
    public Boolean hHasKey(String key, String hashKey) {
        return stringRedisTemplate.opsForHash().hasKey(key, hashKey);
    }

    @Override
    public Long hDelete(String key, String... hashKeys) {
        return stringRedisTemplate.opsForHash().delete(key, hashKeys);
    }

    @Override
    public Boolean existsHashKey(String key, String field) {
        return stringRedisTemplate.opsForHash().hasKey(key, field);
    }

    // -------------------- List 操作 --------------------
    @Override
    public Long lPush(String key, String value) {
        return stringRedisTemplate.opsForList().leftPush(key, value);
    }

    @Override
    public String lPop(String key) {
        return stringRedisTemplate.opsForList().leftPop(key);
    }

    @Override
    public Long rPush(String key, String value) {
        return stringRedisTemplate.opsForList().rightPush(key, value);
    }

    @Override
    public String rPop(String key) {
        return stringRedisTemplate.opsForList().rightPop(key);
    }

    @Override
    public List<String> lRange(String key, long start, long end) {
        return stringRedisTemplate.opsForList().range(key, start, end);
    }

    // -------------------- Set 操作 --------------------
    @Override
    public Long sAdd(String key, String... values) {
        return stringRedisTemplate.opsForSet().add(key, values);
    }

    @Override
    public Set<String> sMembers(String key) {
        return stringRedisTemplate.opsForSet().members(key);
    }

    @Override
    public Boolean sIsMember(String key, String value) {
        return stringRedisTemplate.opsForSet().isMember(key, value);
    }

    @Override
    public Long sRemove(String key, String... values) {
        return stringRedisTemplate.opsForSet().remove(key, (Object[]) values);
    }

    // -------------------- ZSet 操作 --------------------
    @Override
    public Boolean zAdd(String key, String value, double score) {
        return stringRedisTemplate.opsForZSet().add(key, value, score);
    }

    @Override
    public Set<String> zRange(String key, long start, long end) {
        return stringRedisTemplate.opsForZSet().range(key, start, end);
    }

    // -------------------- Lua 脚本 --------------------
    @Override
    public <T> T executeScript(String script, Class<T> resultType, List<String> keys, List<String> args) {
        DefaultRedisScript<T> redisScript = new DefaultRedisScript<>(script, resultType);
        return stringRedisTemplate.execute(redisScript, keys, args.toArray(new Object[0]));
    }

    @Override
    public boolean tryGetDistributedLock(String lockKey, String requestId, int expireTime) {
        /**
         * 第二个和第三个参数不能写如下方式，lua脚本不能正确解析字符串
         * Arrays.asList(requestId, String.valueOf(expireTime))，要重新输入bean到spring {@link WebMvcConfig#redisTemplate(RedisConnectionFactory))}
         * java.util.Arrays$ArrayList cannot be cast to java.lang.String
         */
        try {
            Long result = executeScript(
                    TRY_LOCK_SCRIPT, Long.class,
                    Collections.singletonList(lockKey),
                    Arrays.asList(requestId, String.valueOf(expireTime))
            );
            boolean acquired = result != null && result == 1;
            if (acquired) {
                renewExpire(lockKey, requestId, expireTime);
            }
            return acquired;
        } catch (Exception e) {
            logger.error("锁获取异常: {}@{}", lockKey, requestId, e);
            return false;
        }
    }

    @Override
    public boolean releaseDistributedLock(String lockKey, String requestId) {

        try {
            Long result = executeScript(
                    RELEASE_LOCK_SCRIPT, Long.class,
                    Collections.singletonList(lockKey),
                    Collections.singletonList(requestId)
            );
            return result != null && result > 0;
        } catch (Exception e) {
            logger.error("锁释放异常: {}@{}", lockKey, requestId, e);
            return false;
        }
    }
    /**
     * <p>Description: 自动续期</p>
     * @param lockKey        key
     * @param requestId     被锁的线程
     * @param expireTime    过期时间
     * Timer底层是毫秒(expireTime * 1000L) / 3 ；即到过期时间的三分之一时，自动续成设置的值
     */
    private void renewExpire(String lockKey, String requestId, int expireTime) {
        scheduler.schedule(() -> {
            if (renewLock(lockKey, requestId, expireTime)) {
                renewExpire(lockKey, requestId, expireTime);
            }
        }, expireTime / 3, TimeUnit.SECONDS);
    }

    private boolean renewLock(String lockKey, String requestId, int expireTime) {
        try {
            return Boolean.TRUE.equals(
                    executeScript(
                            RENEW_SCRIPT, Boolean.class,
                            Collections.singletonList(lockKey),
                            Arrays.asList(requestId, String.valueOf(expireTime))
                    )
            );
        } catch (Exception e) {
            logger.error("续期失败", e);
            return false;
        }
    }

}