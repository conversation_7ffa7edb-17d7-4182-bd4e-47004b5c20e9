package com.mebotx.xtand.modules.system.controller;
import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.exception.BaseBizException;
import com.mebotx.xtand.common.exception.BusinessExceptionEnum;
import com.mebotx.xtand.common.vo.resp.JsonResult;
import com.mebotx.xtand.modules.system.service.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;


@Api(tags = {"【文件操作】相关接口", Constants.APP_API_GROUP,Constants.ADMIN_API_GROUP})
@RestController
@RequestMapping("/api/files")
@Slf4j
public class FileController {


    @Resource
    FileService fileService;

    @ApiOperation(value = "文件上传")
    @PostMapping("/upload")
    public JsonResult<String> uploadFile(@RequestParam("file") MultipartFile file) {

       String filePath= fileService.uploadFile(file);
       if(StringUtils.isBlank(filePath)){
            throw new BaseBizException(BusinessExceptionEnum.UPLOAD_FILE_ERROR);
       }
       return JsonResult.buildSuccess(filePath);
    }

    @ApiOperation(value = "文件下载")
    @GetMapping("/download/{fileName}")
    public JsonResult<Void> downloadFile(@PathVariable(name="fileName") String fileName, HttpServletResponse response) {

        InputStream inputStream =null;
        OutputStream outputStream =null;
        try {
             inputStream = fileService.downloadFile(fileName);

             // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            // 获取响应输出流
            outputStream = response.getOutputStream();

            // 缓冲区大小
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 刷新输出流
            outputStream.flush();


        }catch (Exception ex){
            log.error("download file occur error" ,ex);

        }finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("close input stream occur error", e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("close output stream occur error", e);
                }
            }

        }
        return null;
    }
}
