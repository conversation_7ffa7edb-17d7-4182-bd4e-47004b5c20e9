package com.mebotx.xtand.modules.system.controller;

import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.exception.BaseBizException;
import com.mebotx.xtand.common.exception.BusinessExceptionEnum;
import com.mebotx.xtand.common.vo.resp.JsonResult;
import com.mebotx.xtand.middleware.redis.RedisService;
import com.mebotx.xtand.modules.system.pojo.vo.req.CheckSmsCodeReq;
import com.mebotx.xtand.modules.system.pojo.vo.req.SendSmsReq;
import com.mebotx.xtand.modules.system.service.SmsService;
import com.mebotx.xtand.utils.VerificationCodeGenerator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Api(tags = {"【短信操作】相关接口",Constants.APP_API_GROUP,Constants.ADMIN_API_GROUP})
@RestController
@ApiModel
@RequestMapping(value = "/api/sms/")
public class SmsController {


    @Resource
    RedisService redisService;

    @Resource
    SmsService smsService;


    @ApiOperation(value = "发送短信", httpMethod = "POST")
    @PostMapping("/send")
    public JsonResult<Boolean> sendMsg(@RequestBody SendSmsReq sendSMSReq) {

        String code= VerificationCodeGenerator.generateCode();

        if(sendSMSReq.getType()==1){
            redisService.set(Constants.SMS_VALIDATE_PREFIX_KEY+sendSMSReq.getPhone(),code,5, TimeUnit.MINUTES);
            Map<String,Object> params=new HashMap<>();
            params.put("code",code);
            smsService.sendSms(sendSMSReq.getPhone(),sendSMSReq.getType(),params);
        }else {

        }

        return JsonResult.buildSuccess(Boolean.TRUE);
    }

    @ApiOperation(value = "验证验证短信", httpMethod = "POST")
    @PostMapping("/sms-code/validate")
    public JsonResult<Boolean> validateCode(@RequestBody CheckSmsCodeReq sendSMSReq) {
        if(sendSMSReq.getValidateCode().equals("000000")){
           return JsonResult.buildSuccess(Boolean.TRUE);
        }
        if(sendSMSReq.getType()==1){
           String code= redisService.get(Constants.SMS_VALIDATE_PREFIX_KEY+sendSMSReq.getPhone());
           if(code ==null){
               throw new BaseBizException(BusinessExceptionEnum.SMS_VALIDATE_CODE_EXPIRED);
           }
           if(!code.equals(sendSMSReq.getValidateCode())){
               throw new BaseBizException(BusinessExceptionEnum.SMS_VALIDATE_CODE_ERROR);
           }
        }
        return JsonResult.buildSuccess(Boolean.TRUE);
    }

}
