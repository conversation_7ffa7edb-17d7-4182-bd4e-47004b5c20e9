package com.mebotx.xtand.modules.system.controller;

import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.vo.resp.JsonResult;
import com.mebotx.xtand.modules.system.pojo.vo.req.MenuAddReq;
import com.mebotx.xtand.modules.system.pojo.vo.req.MenuEditReq;
import com.mebotx.xtand.modules.system.pojo.vo.resp.MenuResp;
import com.mebotx.xtand.modules.system.service.MenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 菜单管理接口
 */
@Api(tags = {"菜单管理",Constants.ADMIN_API_GROUP})
@RestController
@RequestMapping("/admin/api/menu")
public class SysMenuController {
    @Resource
    private MenuService menuService;

    @ApiOperation("菜单树/列表")
    @GetMapping("/list")
    public JsonResult<List<MenuResp>> list() {
        return JsonResult.buildSuccess(menuService.treeList());
    }

    @ApiOperation("新增菜单")
    @PostMapping("/add")
    public JsonResult<?> add(@RequestBody @Valid MenuAddReq req) {
        menuService.add(req);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("编辑菜单")
    @PostMapping("/edit")
    public JsonResult<?> edit(@RequestBody @Valid MenuEditReq req) {
        menuService.edit(req);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("删除菜单")
    @PostMapping("/delete/{id}")
    public JsonResult<?> delete(@PathVariable Long id) {
        menuService.delete(id);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("菜单详情")
    @GetMapping("/detail/{id}")
    public JsonResult<MenuResp> detail(@PathVariable Long id) {
        return JsonResult.buildSuccess(menuService.detail(id));
    }

    @ApiOperation("查询角色已分配菜单ID")
    @GetMapping("/roleMenuIds/{roleId}")
    public JsonResult<List<Long>> getMenuIdsByRole(@PathVariable Long roleId) {
        return JsonResult.buildSuccess(menuService.getMenuIdsByRole(roleId));
    }

    @ApiOperation("角色分配菜单")
    @PostMapping("/assignMenu")
    public JsonResult<?> assignMenu(@RequestParam Long roleId, @RequestBody List<Long> menuIdList) {
        menuService.assignMenu(roleId, menuIdList);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("当前用户菜单权限")
    @GetMapping("/myMenu")
    public JsonResult<Map<String, Object>> myMenu() {
        String userId = com.mebotx.xtand.web.RequestHeaderHolder.getSessionUser().getUserId();
        Map<String, Object> data = menuService.getUserMenuAndPermissions(Long.valueOf(userId));
        return JsonResult.buildSuccess(data);
    }
} 