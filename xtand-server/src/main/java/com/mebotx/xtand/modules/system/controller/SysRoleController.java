package com.mebotx.xtand.modules.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.vo.resp.JsonResult;
import com.mebotx.xtand.common.vo.resp.PagingInfo;
import com.mebotx.xtand.modules.system.pojo.vo.req.RoleAddReq;
import com.mebotx.xtand.modules.system.pojo.vo.req.RoleEditReq;
import com.mebotx.xtand.modules.system.pojo.vo.resp.RoleResp;
import com.mebotx.xtand.modules.system.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 角色管理接口
 */
@Api(tags = {"角色管理", Constants.ADMIN_API_GROUP})
@RestController
@RequestMapping("/admin/api/role")
public class SysRoleController {
    @Resource
    private RoleService roleService;

    @ApiOperation("角色分页查询")
    @GetMapping("/list")
    public JsonResult<PagingInfo<RoleResp>> list(@RequestParam(required = false) String name,
                                                 @RequestParam(required = false) Integer status,
                                                 @RequestParam(defaultValue = "1") int pageNum,
                                                 @RequestParam(defaultValue = "10") int pageSize) {
        return JsonResult.buildSuccess(roleService.pageList(name, status, pageNum, pageSize));
    }

    @ApiOperation("新增角色")
    @PostMapping("/add")
    public JsonResult<?> add(@RequestBody @Valid RoleAddReq req) {
        roleService.add(req);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("编辑角色")
    @PostMapping("/edit")
    public JsonResult<?> edit(@RequestBody @Valid RoleEditReq req) {
        roleService.edit(req);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("删除角色")
    @PostMapping("/delete/{id}")
    public JsonResult<?> delete(@PathVariable Long id) {
        roleService.delete(id);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("角色详情")
    @GetMapping("/detail/{id}")
    public JsonResult<RoleResp> detail(@PathVariable Long id) {
        return JsonResult.buildSuccess(roleService.detail(id));
    }

    @ApiOperation("查询所有角色")
    @GetMapping("/all")
    public JsonResult<List<RoleResp>> all() {
        return JsonResult.buildSuccess(roleService.all());
    }
} 