package com.mebotx.xtand.modules.system.controller;

import com.mebotx.xtand.common.vo.resp.JsonResult;
import com.mebotx.xtand.common.vo.resp.PagingInfo;
import com.mebotx.xtand.modules.system.pojo.vo.req.*;
import com.mebotx.xtand.modules.system.pojo.vo.resp.SysLoginResp;
import com.mebotx.xtand.modules.system.pojo.vo.resp.SysUserResp;
import com.mebotx.xtand.modules.system.pojo.vo.resp.UserRoleResp;
import com.mebotx.xtand.modules.system.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "系统用户管理接口")
@RestController
@RequestMapping("/admin/api/sysUser")
public class SysUserController {

    ///admin/api/sysUser/login
    @Resource
    private SysUserService sysUserService;


    @ApiOperation(value = "管理用户登录", httpMethod = "POST")
    @PostMapping("/login")
    public JsonResult<SysLoginResp> sysLogin(@RequestBody @Valid SysLoginReq req) {
        SysLoginResp resp = sysUserService.sysLogin(req);
        return JsonResult.buildSuccess(resp);
    }


    /**
     * 用户分页查询
     */
    @ApiOperation("用户分页查询")
    @GetMapping("/list")
    JsonResult<PagingInfo<SysUserResp>>  list(SysUserReq req) {
        return JsonResult.buildSuccess(sysUserService.pageList(req));
    }

    /**
     * 新增用户
     */
    @ApiOperation("新增用户")
    @PostMapping("/add")
    public JsonResult<Void> add(@RequestBody @Valid SysUserAddReq req) {
        sysUserService.add(req);
        return JsonResult.buildSuccess();
    }

    /**
     * 编辑用户
     */
    @ApiOperation("编辑用户")
    @PostMapping("/edit")
    public JsonResult<Void> edit(@RequestBody @Valid SysUserEditReq req) {
        sysUserService.edit(req);
        return JsonResult.buildSuccess();
    }

    /**
     * 删除用户
     */
    @ApiOperation("删除用户")
    @PostMapping("/delete/{userId}")
    public JsonResult<Void> delete(@PathVariable Long userId) {
        sysUserService.delete(userId);
        return JsonResult.buildSuccess();
    }

    /**
     * 用户详情
     */
    @ApiOperation("用户详情")
    @GetMapping("/detail/{userId}")
    public JsonResult<SysUserResp> detail(@PathVariable Long userId) {
        return JsonResult.buildSuccess(sysUserService.detail(userId));
    }


    @ApiOperation(value = "管理员重置密码", httpMethod = "POST")
    @PostMapping("/resetUserPassword")
    public JsonResult<Void> resetPassword(@RequestBody @Valid ResetPasswordReq req) {
        sysUserService.resetPassword(req);
        return JsonResult.buildSuccess();
    }

    /**
     * 分配角色
     */
    @ApiOperation("分配角色")
    @PostMapping("/assignRoles")
    public JsonResult<Void> assignRoles(@RequestBody @Valid UserRoleAssignReq req) {
        sysUserService.assignRoles(req);
        return JsonResult.buildSuccess();
    }

    /**
     * 查询用户已分配角色
     */
    @ApiOperation("查询用户已分配角色")
    @GetMapping("/roles/{userId}")
    public JsonResult<UserRoleResp> getUserRoles(@PathVariable Long userId) {
        return JsonResult.buildSuccess(sysUserService.getUserRoles(userId));
    }
}
