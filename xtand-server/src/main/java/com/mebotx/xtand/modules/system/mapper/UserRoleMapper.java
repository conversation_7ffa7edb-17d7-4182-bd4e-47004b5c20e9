package com.mebotx.xtand.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mebotx.xtand.modules.system.pojo.entity.SysUserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户-角色关联表Mapper接口
 */
@Mapper
public interface UserRoleMapper extends BaseMapper<SysUserRole> {
    /**
     * 批量插入用户-角色关联
     */
    int insertBatchSomeColumn(@Param("list") List<SysUserRole> list);
} 