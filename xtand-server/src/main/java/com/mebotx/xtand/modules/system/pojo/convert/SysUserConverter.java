package com.mebotx.xtand.modules.system.pojo.convert;

import com.mebotx.xtand.modules.system.pojo.entity.SysUser;
import com.mebotx.xtand.modules.system.pojo.vo.resp.SysUserResp;
import org.mapstruct.MapMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/31 8:14
 * @Desc
 */
@Mapper(componentModel = "spring")
public interface SysUserConverter {

    List<SysUserResp> toRespList(List<SysUser> records);



//    @Mapping(target = "roles", expression = "java(sysUser.getRoles() != null ? sysUser.getRoles().split(\",\") : null)")
//    SysUserResp toResp(SysUser sysUser);
}
