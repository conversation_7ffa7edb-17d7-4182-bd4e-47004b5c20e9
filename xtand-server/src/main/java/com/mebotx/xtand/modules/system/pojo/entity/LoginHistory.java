package com.mebotx.xtand.modules.system.pojo.entity;


import java.io.Serializable;


import com.mebotx.xtand.common.CommonEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
* 
* @TableName login_history
*/


@TableName(value = "login_history")
@Data
public class LoginHistory extends CommonEntity implements Serializable{

    /**
    * 主键
    */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
    * 用户名
    */
    private String userName;
    /**
    * 真实姓名
    */
    private String realName;
    /**
    * 角色
    */
    private String roles;
    /**
    * login-登陆 logout登出
    */
    private String type;
    /**
    * 用户ID
    */
    private Long userId;

    /**
    * 访问终端
    */
    private String clientType;
    /**
    * 访问IP
    */
    private String clientIp;
    /**
    * 设备
    */
    private String device;



}
