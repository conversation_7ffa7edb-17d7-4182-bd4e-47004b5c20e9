package com.mebotx.xtand.modules.system.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mebotx.xtand.common.CommonEntity;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 菜单/按钮实体
 */
@Data
@TableName("sys_menu")
public class SysMenu extends CommonEntity implements Serializable {
    /** 菜单ID */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 父菜单ID（0为顶级） */
    private Long parentId;
    /** 菜单名称 */
    private String name;
    /** 菜单类型（0目录 1菜单 2按钮） */
    private Integer type;
    /** 权限标识（如sys:user:add） */
    private String permission;
    /** 路由地址 */
    private String path;
    /** 图标 */
    private String icon;
    /** 排序 */
    private Integer sort;
    /** 状态（1启用 0禁用） */
    private Integer status;
    /**是否系统菜单（1是 0否） */
    private Integer isSystem;

} 