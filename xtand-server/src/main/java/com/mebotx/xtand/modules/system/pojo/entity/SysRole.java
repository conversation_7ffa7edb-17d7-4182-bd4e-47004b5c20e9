package com.mebotx.xtand.modules.system.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mebotx.xtand.common.CommonEntity;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 角色实体
 */
@Data
@TableName("sys_role")
public class SysRole extends CommonEntity implements Serializable {
    /** 角色ID */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 角色名称 */
    private String name;
    /** 角色编码（如admin、user） */
    private String code;
    /** 角色描述 */
    private String description;
    /** 状态（1启用 0禁用） */
    private Integer status;
    /**是否系统角色：1是，0否**/
    private Integer isSystem;

} 