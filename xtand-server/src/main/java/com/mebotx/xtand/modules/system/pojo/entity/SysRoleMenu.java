package com.mebotx.xtand.modules.system.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mebotx.xtand.common.CommonEntity;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 角色-菜单关联实体
 */
@Data
@TableName("sys_role_menu")
public class SysRoleMenu extends CommonEntity implements Serializable {
    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 角色ID */
    private Long roleId;
    /** 菜单ID */
    private Long menuId;

} 