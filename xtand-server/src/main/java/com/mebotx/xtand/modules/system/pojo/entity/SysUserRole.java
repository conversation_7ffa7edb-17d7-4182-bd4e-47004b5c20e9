package com.mebotx.xtand.modules.system.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mebotx.xtand.common.CommonEntity;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户-角色关联实体
 */
@Data
@TableName("sys_user_role")
public class SysUserRole extends CommonEntity implements Serializable {
    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 用户ID */
    private Long userId;
    /** 角色ID */
    private Long roleId;

} 