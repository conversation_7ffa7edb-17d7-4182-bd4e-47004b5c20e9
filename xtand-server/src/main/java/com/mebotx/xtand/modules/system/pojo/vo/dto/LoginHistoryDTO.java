package com.mebotx.xtand.modules.system.pojo.vo.dto;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class LoginHistoryDTO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 真实姓名
     */
    private String realName;
    /**
     * 角色
     */
    private String roles;
    /**
     * login-登陆 logout登出
     */
    private String type;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 访问终端
     */
    private String clientType;
    /**
     * 访问IP
     */
    private String clientIp;
    /**
     * 设备
     */
    private String device;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;
}
