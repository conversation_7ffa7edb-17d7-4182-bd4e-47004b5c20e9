package com.mebotx.xtand.modules.system.pojo.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data

@ApiModel
public class CheckSmsCodeReq {

    @ApiModelProperty(value="场景 （1-认证 2-找回密码）")
    private int type;

    @ApiModelProperty(value="手机号",example = "13403478734")
    private String phone;

    @ApiModelProperty(value="验证吗",example = "555555")
    private String validateCode;

}
