package com.mebotx.xtand.modules.system.pojo.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 菜单新增请求对象
 */
@Data
public class MenuAddReq implements Serializable {
    @ApiModelProperty(value = "父菜单ID（0为顶级）", required = true)
    @NotNull(message = "父菜单ID不能为空")
    private Long parentId;

    @ApiModelProperty(value = "菜单名称", required = true)
    @NotBlank(message = "菜单名称不能为空")
    private String name;

    @ApiModelProperty(value = "菜单类型（0目录 1菜单 2按钮）", required = true)
    @NotNull(message = "菜单类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "权限标识")
    private String permission;

    @ApiModelProperty(value = "路由地址")
    private String path;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "状态（1启用 0禁用）")
    private Integer status;
} 