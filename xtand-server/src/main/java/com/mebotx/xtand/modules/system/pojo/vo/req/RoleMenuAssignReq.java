package com.mebotx.xtand.modules.system.pojo.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 角色分配菜单请求对象
 */
@Data
public class RoleMenuAssignReq implements Serializable {
    @ApiModelProperty(value = "角色ID", required = true)
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @ApiModelProperty(value = "菜单ID列表", required = true)
    @NotNull(message = "菜单ID列表不能为空")
    private List<Long> menuIdList;
} 