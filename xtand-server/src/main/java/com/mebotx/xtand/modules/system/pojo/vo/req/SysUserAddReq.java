package com.mebotx.xtand.modules.system.pojo.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/3/31 8:25
 * @Desc
 */
@Data
public class SysUserAddReq implements Serializable {


    @ApiModelProperty(value = "用户名", required = true)
    private String userName;

    @ApiModelProperty(value = "用户真实姓名")
    private String realName;

    @ApiModelProperty(value = "用户邮箱")
    private String email;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "头像地址")
    private String avatar;

    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @ApiModelProperty(value = "角色ID列表")
    private java.util.List<Long> roleIdList;


}
