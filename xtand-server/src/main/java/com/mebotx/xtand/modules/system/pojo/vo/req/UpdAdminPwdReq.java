package com.mebotx.xtand.modules.system.pojo.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/3/31 8:25
 * @Desc
 */
@Data
public class UpdAdminPwdReq implements Serializable {

    @NotBlank(message = "被修改的用户id")
    @ApiModelProperty("用户id")
    private String userId;

    @NotBlank(message = "新密码不能为空")
    @ApiModelProperty("新密码")
    private String password;

    @ApiModelProperty("老密码")
    private String oldPassword;
}
