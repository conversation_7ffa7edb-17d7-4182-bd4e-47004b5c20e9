package com.mebotx.xtand.modules.system.pojo.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 用户分配角色请求对象
 */
@Data
public class UserRoleAssignReq implements Serializable {
    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "角色ID列表", required = true)
    @NotNull(message = "角色ID列表不能为空")
    private List<Long> roleIdList;
} 