package com.mebotx.xtand.modules.system.pojo.vo.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 菜单响应对象
 */
@Data
public class MenuResp implements Serializable {
    @ApiModelProperty(value = "菜单ID")
    private Long id;

    @ApiModelProperty(value = "父菜单ID（0为顶级）")
    private Long parentId;

    @ApiModelProperty(value = "菜单名称")
    private String name;

    @ApiModelProperty(value = "菜单类型（0目录 1菜单 2按钮）")
    private Integer type;

    @ApiModelProperty(value = "权限标识")
    private String permission;

    @ApiModelProperty(value = "路由地址")
    private String path;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "状态（1启用 0禁用）")
    private Integer status;

    @ApiModelProperty(value = "是否系统菜单：1是，0否")
    private Integer isSystem;

    @ApiModelProperty(value = "子菜单")
    private List<MenuResp> children;
} 