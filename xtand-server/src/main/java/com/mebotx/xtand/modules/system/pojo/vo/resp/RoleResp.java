package com.mebotx.xtand.modules.system.pojo.vo.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * 角色响应对象
 */
@Data
public class RoleResp implements Serializable {
    @ApiModelProperty(value = "角色ID")
    private Long id;

    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "角色编码")
    private String code;

    @ApiModelProperty(value = "角色描述")
    private String description;

    @ApiModelProperty(value = "状态（1启用 0禁用）")
    private Integer status;

    @ApiModelProperty(value = "是否系统角色：1是，0否")
    private Integer isSystem;
} 