package com.mebotx.xtand.modules.system.pojo.vo.resp;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/31 7:57
 * @Desc
 */
@Data
public class SysUserResp implements Serializable {

    @ApiModelProperty(value = "用户ID")
    private Long id;

    /**
     * 用户手机
     */
    @ApiModelProperty(value = "用户手机")
    private String phone;

    /**
     * 用户账号
     */
    @ApiModelProperty(value = "用户名")
    private String userName;



    /**
     * 用户邮箱
     */
    @ApiModelProperty(value = "用户邮箱")
    private String email;


    @ApiModelProperty(value = "用户状态")
    private Integer status;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "角色名称列表")
    private List<String> roleNames;

    @ApiModelProperty(value = "角色ID列表")
    private java.util.List<Long> roleIdList;


}
