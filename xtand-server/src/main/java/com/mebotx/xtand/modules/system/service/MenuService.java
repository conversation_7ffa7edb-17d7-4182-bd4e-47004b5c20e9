package com.mebotx.xtand.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mebotx.xtand.modules.system.pojo.entity.SysMenu;
import com.mebotx.xtand.modules.system.pojo.vo.req.MenuAddReq;
import com.mebotx.xtand.modules.system.pojo.vo.req.MenuEditReq;
import com.mebotx.xtand.modules.system.pojo.vo.resp.MenuResp;
import java.util.List;
import java.util.Map;

/**
 * 菜单服务接口
 */
public interface MenuService extends IService<SysMenu> {
    /**
     * 菜单树/列表
     */
    List<MenuResp> treeList();

    /**
     * 新增菜单
     */
    void add(MenuAddReq req);

    /**
     * 编辑菜单
     */
    void edit(MenuEditReq req);

    /**
     * 删除菜单
     */
    void delete(Long id);

    /**
     * 菜单详情
     */
    MenuResp detail(Long id);

    /**
     * 角色已分配菜单ID列表
     */
    List<Long> getMenuIdsByRole(Long roleId);

    /**
     * 角色分配菜单
     */
    void assignMenu(Long roleId, List<Long> menuIdList);

    /**
     * 校验菜单业务编码唯一
     */
    boolean checkCodeUnique(String code, Long excludeId);

    /**
     * 获取当前用户拥有的菜单和按钮权限
     */
    Map<String, Object> getUserMenuAndPermissions(Long userId);
} 