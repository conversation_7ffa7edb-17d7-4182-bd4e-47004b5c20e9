package com.mebotx.xtand.modules.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mebotx.xtand.common.vo.resp.PagingInfo;
import com.mebotx.xtand.modules.system.pojo.entity.SysRole;
import com.mebotx.xtand.modules.system.pojo.vo.req.RoleAddReq;
import com.mebotx.xtand.modules.system.pojo.vo.req.RoleEditReq;
import com.mebotx.xtand.modules.system.pojo.vo.resp.RoleResp;

import java.util.List;

/**
 * 角色服务接口
 */
public interface RoleService extends IService<SysRole> {
    /**
     * 角色分页查询
     */
    PagingInfo<RoleResp> pageList(String name, Integer status, int pageNum, int pageSize);

    /**
     * 新增角色
     */
    void add(RoleAddReq req);

    /**
     * 编辑角色
     */
    void edit(RoleEditReq req);

    /**
     * 删除角色
     */
    void delete(Long id);

    /**
     * 角色详情
     */
    RoleResp detail(Long id);

    /**
     * 查询所有角色（下拉用）
     */
    List<RoleResp> all();
} 