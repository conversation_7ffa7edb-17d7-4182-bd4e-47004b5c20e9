package com.mebotx.xtand.modules.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mebotx.xtand.common.vo.resp.PagingInfo;
import com.mebotx.xtand.modules.system.pojo.entity.SysUser;
import com.mebotx.xtand.modules.system.pojo.vo.req.*;
import com.mebotx.xtand.modules.system.pojo.vo.resp.SysLoginResp;
import com.mebotx.xtand.modules.system.pojo.vo.resp.SysUserResp;
import com.mebotx.xtand.modules.system.pojo.vo.resp.UserRoleResp;

/**
 * 系统用户服务接口
 */
public interface SysUserService extends IService<SysUser> {


    /**
     * 管理用户登录
     */
    SysLoginResp sysLogin(SysLoginReq req);

    /**
     * 用户分页查询
     */
    public PagingInfo<SysUserResp> pageList(SysUserReq req);

    /**
     * 新增用户
     */
    void add(SysUserAddReq req);

    /**
     * 编辑用户
     */
    void edit(SysUserEditReq req);

    /**
     * 删除用户
     */
    void delete(Long userId);

    /**
     * 用户详情
     */
    SysUserResp detail(Long userId);

    /**
     * 分配角色
     */
    void assignRoles(UserRoleAssignReq req);

    /**
     * 查询用户已分配角色
     */
    UserRoleResp getUserRoles(Long userId);

    void resetPassword(ResetPasswordReq req);
}
