package com.mebotx.xtand.modules.system.service.impl;

import com.mebotx.xtand.common.properties.MinioProperties;
import com.mebotx.xtand.modules.system.service.FileService;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.UUID;

@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Resource
    @Autowired
    private MinioClient minioClient;


    @Resource
    MinioProperties minioProperties;



    public String uploadFile(MultipartFile file) {

        try {
            String originalFilename=file.getOriginalFilename();
            int dotIndex = originalFilename.lastIndexOf(".");
            String extend="";
            // 如果最后一个 "." 在文件名开头（如 ".gitignore"），返回空
            if (dotIndex !=-1) {
                extend=originalFilename.substring(dotIndex).toLowerCase();
            }

            // 截取扩展名并转为小写（示例：返回 "txt"）

            String fileName= UUID.randomUUID().toString()+extend;
            InputStream inputStream = file.getInputStream();
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(minioProperties.getBucketName())
                    .object(fileName)
                    .stream(inputStream, file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build());
            inputStream.close();
            // 构建文件的访问路径
            return minioProperties.getEndpoint() + "/" +minioProperties.getBucketName() + "/" + fileName;
        }catch (Exception ex){
            log.error("upload file error",ex);
        }

        return null;

    }

    public InputStream downloadFile(String objectName) {

        try {
            return minioClient.getObject(GetObjectArgs.builder()
                    .bucket(minioProperties.getBucketName())
                    .object(objectName)
                    .build());
        }catch (Exception ex){
            log.error("download file error",ex);
            return null;
        }
    }


}
