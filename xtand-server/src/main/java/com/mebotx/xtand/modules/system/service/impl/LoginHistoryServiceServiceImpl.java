package com.mebotx.xtand.modules.system.service.impl;

import com.mebotx.xtand.common.constant.RoleEnum;
import com.mebotx.xtand.modules.system.mapper.LoginHistoryMapper;
import com.mebotx.xtand.modules.system.pojo.entity.LoginHistory;
import com.mebotx.xtand.modules.system.service.LoginHistoryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;



@Service
public class LoginHistoryServiceServiceImpl extends ServiceImpl<LoginHistoryMapper, LoginHistory> implements LoginHistoryService {

    @Override
    public LoginHistory getLastLogin() {


        // 创建查询条件
        LambdaQueryWrapper<LoginHistory> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.like(LoginHistory::getRoles, RoleEnum.DOORKEEPER_ROLE.getCode());
        // 按登录时间降序排列（最新的在前）
        queryWrapper.orderByDesc(LoginHistory::getCreatedTime);

        // 只取第一条记录（最新的）
        Page<LoginHistory> page = new Page<>(1, 1); // 第1页，每页1条

        // 执行查询
        Page<LoginHistory> resultPage = baseMapper.selectPage(page, queryWrapper);

        // 返回结果
        return resultPage.getRecords().stream().findFirst().orElse(null);

    }
}
