package com.mebotx.xtand.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mebotx.xtand.modules.system.mapper.MenuMapper;
import com.mebotx.xtand.modules.system.mapper.RoleMenuMapper;
import com.mebotx.xtand.modules.system.mapper.UserRoleMapper;
import com.mebotx.xtand.modules.system.mapper.SysUserMapper;
import com.mebotx.xtand.modules.system.pojo.entity.SysMenu;
import com.mebotx.xtand.modules.system.pojo.entity.SysRoleMenu;
import com.mebotx.xtand.modules.system.pojo.entity.SysUserRole;
import com.mebotx.xtand.modules.system.pojo.entity.SysUser;
import com.mebotx.xtand.modules.system.pojo.vo.req.MenuAddReq;
import com.mebotx.xtand.modules.system.pojo.vo.req.MenuEditReq;
import com.mebotx.xtand.modules.system.pojo.vo.resp.MenuResp;
import com.mebotx.xtand.modules.system.service.MenuService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Collections;

/**
 * 菜单服务实现
 */
@Service
public class MenuServiceImpl extends ServiceImpl<MenuMapper, SysMenu> implements MenuService {
    private final RoleMenuMapper roleMenuMapper;
    private final UserRoleMapper userRoleMapper;
    private final SysUserMapper sysUserMapper;
    public MenuServiceImpl(RoleMenuMapper roleMenuMapper, UserRoleMapper userRoleMapper, SysUserMapper sysUserMapper) {
        this.roleMenuMapper = roleMenuMapper;
        this.userRoleMapper = userRoleMapper;
        this.sysUserMapper = sysUserMapper;
    }

    @Override
    public List<MenuResp> treeList() {
        List<SysMenu> allMenus = this.list();
        return buildTree(0L, allMenus);
    }

    private List<MenuResp> buildTree(Long parentId, List<SysMenu> allMenus) {
        List<MenuResp> tree = new ArrayList<>();
        for (SysMenu menu : allMenus) {
            if (Objects.equals(menu.getParentId(), parentId)) {
                MenuResp resp = new MenuResp();
                BeanUtils.copyProperties(menu, resp);
                List<MenuResp> children = buildTree(menu.getId(), allMenus);
                if (!children.isEmpty()) {
                    resp.setChildren(children);
                                }
                tree.add(resp);
            }
        }
        return tree;
    }

    @Override
    @Transactional
    public void add(MenuAddReq req) {
        // 业务编码唯一性校验
        if (!checkCodeUnique(req.getPermission(), null)) {
            throw new RuntimeException("业务编码已存在");
        }
        SysMenu menu = new SysMenu();
        BeanUtils.copyProperties(req, menu);

        boolean isSystem=false;
        if(menu.getParentId() != null ){

            if(menu.getParentId() != 0) {

                SysMenu sysMenu = getById(menu.getParentId());
                if (sysMenu.getIsSystem() == 1) {
                    menu.setIsSystem(1);
                    isSystem = true;
                }
            }
        }

        this.save(menu);

        //添加非系统菜单,需要默认给系统管理员授权
        if(!isSystem){
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(1L);
            rm.setMenuId(menu.getId());
            roleMenuMapper.insert(rm);
        }


    }

    @Override
    @Transactional
    public void edit(MenuEditReq req) {
        SysMenu menu = this.getById(req.getId());
        if (menu == null) throw new RuntimeException("菜单不存在");
        if (!checkCodeUnique(req.getPermission(), req.getId())) {
            throw new RuntimeException("业务编码已存在");
        }
        BeanUtils.copyProperties(req, menu, "id");
        this.updateById(menu);
    }

    @Override
    @Transactional
    public void delete(Long id) {
        // 可扩展：禁止删除系统保留菜单
        this.removeById(id);
    }

    @Override
    public MenuResp detail(Long id) {
        SysMenu menu = this.getById(id);
        if (menu == null) throw new RuntimeException("菜单不存在");
        MenuResp resp = new MenuResp();
        BeanUtils.copyProperties(menu, resp);
        return resp;
    }

    @Override
    public List<Long> getMenuIdsByRole(Long roleId) {
        return roleMenuMapper.selectList(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getRoleId, roleId))
                .stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void assignMenu(Long roleId, List<Long> menuIdList) {
        // 先删除原有
        roleMenuMapper.delete(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getRoleId, roleId));
        // 再插入新分配
        if (menuIdList != null && !menuIdList.isEmpty()) {
            List<SysRoleMenu> list = menuIdList.stream().map(menuId -> {
                SysRoleMenu rm = new SysRoleMenu();
                rm.setRoleId(roleId);
                rm.setMenuId(menuId);
                return rm;
            }).collect(Collectors.toList());
            list.forEach(roleMenuMapper::insert);
        }
    }

    @Override
    public boolean checkCodeUnique(String code, Long excludeId) {
        if (!StringUtils.hasText(code)) return true;
        LambdaQueryWrapper<SysMenu> query = new LambdaQueryWrapper<>();
        query.eq(SysMenu::getPermission, code);
        if (excludeId != null) {
            query.ne(SysMenu::getId, excludeId);
        }
        return this.count(query) == 0;
    }

    @Override
    public Map<String, Object> getUserMenuAndPermissions(Long userId) {
        // 0. 判断是否超级管理员
        SysUser user = sysUserMapper.selectById(userId);
        if (user != null && user.getIsSuperAdmin() != null && user.getIsSuperAdmin() == 1) {
            // 查询所有菜单
            List<SysMenu> allMenus = this.list();
            // 区分菜单和按钮
            List<SysMenu> menuList = allMenus.stream().filter(m -> m.getType() != 2).collect(Collectors.toList());
            List<String> buttons = allMenus.stream().filter(m -> m.getType() == 2 && m.getPermission() != null)
                .map(SysMenu::getPermission).collect(Collectors.toList());
            List<MenuResp> menuTree = buildTree(0L, menuList);
            Map<String, Object> result = new HashMap<>();
            result.put("menus", menuTree);
            result.put("buttons", buttons);
            return result;
        }
        // 1. 查用户角色
        List<Long> roleIds = userRoleMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId)
        ).stream().map(SysUserRole::getRoleId).collect(Collectors.toList());

        if (roleIds.isEmpty()) {
            Map<String, Object> result = new HashMap<>();
            result.put("menus", Collections.emptyList());
            result.put("buttons", Collections.emptyList());
            return result;
        }

        // 2. 查角色菜单
        List<Long> menuIds = roleMenuMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<com.mebotx.xtand.modules.system.pojo.entity.SysRoleMenu>().in(com.mebotx.xtand.modules.system.pojo.entity.SysRoleMenu::getRoleId, roleIds)
        ).stream().map(com.mebotx.xtand.modules.system.pojo.entity.SysRoleMenu::getMenuId).distinct().collect(Collectors.toList());

        if (menuIds.isEmpty()) {
            Map<String, Object> result = new HashMap<>();
            result.put("menus", Collections.emptyList());
            result.put("buttons", Collections.emptyList());
            return result;
        }

        // 3. 查菜单详情
        List<com.mebotx.xtand.modules.system.pojo.entity.SysMenu> allMenus = this.listByIds(menuIds);

        // 4. 按类型分组
        List<com.mebotx.xtand.modules.system.pojo.entity.SysMenu> menuList = allMenus.stream().filter(m -> m.getType() != 2).collect(Collectors.toList());
        List<String> buttons = allMenus.stream().filter(m -> m.getType() == 2 && m.getPermission() != null)
            .map(com.mebotx.xtand.modules.system.pojo.entity.SysMenu::getPermission).collect(Collectors.toList());

        // 5. 组装树结构
        List<MenuResp> menuTree = buildTree(0L, menuList);

        Map<String, Object> result = new HashMap<>();
        result.put("menus", menuTree);
        result.put("buttons", buttons);
        return result;
    }
} 