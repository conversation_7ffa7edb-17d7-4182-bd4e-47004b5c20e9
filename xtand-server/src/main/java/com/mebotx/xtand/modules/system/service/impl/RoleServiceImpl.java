package com.mebotx.xtand.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.vo.resp.PagingInfo;
import com.mebotx.xtand.modules.system.mapper.RoleMapper;
import com.mebotx.xtand.modules.system.pojo.entity.SysRole;
import com.mebotx.xtand.modules.system.pojo.vo.req.RoleAddReq;
import com.mebotx.xtand.modules.system.pojo.vo.req.RoleEditReq;
import com.mebotx.xtand.modules.system.pojo.vo.resp.RoleResp;
import com.mebotx.xtand.modules.system.service.RoleService;
import com.mebotx.xtand.web.RequestHeaderHolder;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 角色服务实现
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, SysRole> implements RoleService {
    @Override
    public PagingInfo<RoleResp> pageList(String name, Integer status, int pageNum, int pageSize) {
        Page<SysRole> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<SysRole> query = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(name)) {
            query.like(SysRole::getName, name);
        }
        if (status != null) {
            query.eq(SysRole::getStatus, status);
        }
        IPage<SysRole> rolePage = this.page(page, query);
        List<RoleResp> respList = rolePage.getRecords().stream().map(role -> {
            RoleResp resp = new RoleResp();
            BeanUtils.copyProperties(role, resp);
            return resp;
        }).collect(Collectors.toList());


        return PagingInfo.toResponse(respList, rolePage.getTotal(), pageNum, pageSize);
    }

    @Override
    @Transactional
    public void add(RoleAddReq req) {
        // 角色名、编码唯一性校验
        if (this.count(new LambdaQueryWrapper<SysRole>().eq(SysRole::getName, req.getName())) > 0) {
            throw new RuntimeException("角色名称已存在");
        }
        if (this.count(new LambdaQueryWrapper<SysRole>().eq(SysRole::getCode, req.getCode())) > 0) {
            throw new RuntimeException("角色编码已存在");
        }
        SysRole role = new SysRole();
        BeanUtils.copyProperties(req, role);
        this.save(role);
    }

    @Override
    @Transactional
    public void edit(RoleEditReq req) {
        SysRole role = this.getById(req.getId());
        if (role == null) throw new RuntimeException("角色不存在");
        // 超级管理员不可编辑（如有特殊标识可补充）
        if (StringUtils.hasText(req.getName()) && !req.getName().equals(role.getName()) &&
                this.count(new LambdaQueryWrapper<SysRole>().eq(SysRole::getName, req.getName()).ne(SysRole::getId, req.getId())) > 0) {
            throw new RuntimeException("角色名称已存在");
        }
        if (StringUtils.hasText(req.getCode()) && !req.getCode().equals(role.getCode()) &&
                this.count(new LambdaQueryWrapper<SysRole>().eq(SysRole::getCode, req.getCode()).ne(SysRole::getId, req.getId())) > 0) {
            throw new RuntimeException("角色编码已存在");
        }
        BeanUtils.copyProperties(req, role, "id");
        this.updateById(role);
    }

    @Override
    @Transactional
    public void delete(Long id) {
        SysRole role = this.getById(id);
        if (role == null) throw new RuntimeException("角色不存在");
        // 超级管理员不可删除（如有特殊标识可补充）
        this.removeById(id);
    }

    @Override
    public RoleResp detail(Long id) {
        SysRole role = this.getById(id);
        if (role == null) throw new RuntimeException("角色不存在");
        RoleResp resp = new RoleResp();
        BeanUtils.copyProperties(role, resp);
        return resp;
    }

    @Override
    public List<RoleResp> all() {

        List<SysRole> list = this.list();

        List<SysRole>  result=   list.stream().filter(e->e.getStatus()==1).collect(Collectors.toList());

        return result.stream().map(role -> {
            RoleResp resp = new RoleResp();
            BeanUtils.copyProperties(role, resp);
            return resp;
        }).collect(Collectors.toList());
    }
} 