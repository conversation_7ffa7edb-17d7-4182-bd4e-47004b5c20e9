package com.mebotx.xtand.modules.system.service.impl;

import cn.hutool.json.JSONUtil;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.mebotx.xtand.common.exception.BaseBizException;
import com.mebotx.xtand.common.exception.BusinessExceptionEnum;
import com.mebotx.xtand.modules.system.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class SmsServiceImpl implements SmsService {



    @Resource
    Client client;


    @Override
    public Boolean sendSms(String phone, int type, Map<String, Object> params) {
        try {


            String templateParams= JSONUtil.toJsonStr(params);
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phone)
                    .setSignName("迈宝智能科技苏州")
                    .setTemplateCode("SMS_323310010")
                    // TemplateParam为序列化后的JSON字符串。其中\"表示转义后的双引号。
                    .setTemplateParam(templateParams);

            SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);

            // 响应包含服务端响应的 body 和 headers
            log.info(JSONUtil.toJsonStr(sendSmsResponse));
        }catch (Exception ex){
            log.error("send sms error",ex);
            throw new BaseBizException(BusinessExceptionEnum.SEND_SMS_FAIL);
        }

        return Boolean.TRUE;
    }


}
