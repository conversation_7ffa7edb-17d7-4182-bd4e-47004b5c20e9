package com.mebotx.xtand.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.exception.BaseBizException;
import com.mebotx.xtand.common.exception.BusinessExceptionEnum;
import com.mebotx.xtand.common.vo.resp.PagingInfo;
import com.mebotx.xtand.middleware.redis.RedisService;
import com.mebotx.xtand.modules.system.mapper.RoleMapper;
import com.mebotx.xtand.modules.system.mapper.SysUserMapper;
import com.mebotx.xtand.modules.system.mapper.UserRoleMapper;
import com.mebotx.xtand.modules.system.pojo.entity.SysRole;
import com.mebotx.xtand.modules.system.pojo.entity.SysUser;
import com.mebotx.xtand.modules.system.pojo.entity.SysUserRole;
import com.mebotx.xtand.modules.system.pojo.vo.req.*;
import com.mebotx.xtand.modules.system.pojo.vo.resp.SysLoginResp;
import com.mebotx.xtand.modules.system.pojo.vo.resp.SysUserResp;
import com.mebotx.xtand.modules.system.pojo.vo.resp.UserRoleResp;
import com.mebotx.xtand.modules.system.service.SysUserService;
import com.mebotx.xtand.utils.SHA256Util;
import com.mebotx.xtand.web.JWTTokenHelper;
import com.mebotx.xtand.web.RequestHeaderHolder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 系统用户服务实现类
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    RedisService redisService;


    @Override
    public SysLoginResp sysLogin(SysLoginReq req) {

        SysUser sysUser = lambdaQuery().eq(SysUser::getUserName, req.getUserName())
                .or()
                .eq(SysUser::getPhone, req.getUserName())
                .one();
        if (sysUser == null) {
            throw new BaseBizException(BusinessExceptionEnum.USER_INFO_ABSENT);
        }

        String password = sysUser.getPassword();
        if (!Objects.equals(DigestUtils.md5DigestAsHex(req.getPassword().getBytes()), password)) {
            throw new BaseBizException(BusinessExceptionEnum.PASSWORD_ERROR);
        }



        // 创建token
        String token = JWTTokenHelper.generateToken(sysUser.getId(),sysUser.getUserName() ,sysUser.getIsSuperAdmin(),"");
        SysLoginResp resp = new SysLoginResp();
        resp.setUserName(sysUser.getUserName());
        resp.setIsSuperAdmin(sysUser.getIsSuperAdmin());
       // resp.setRoles(sysUser.getRoles().split(","));
        resp.setToken(token);

        redisService.hSet(Constants.WEB_TOKEN_USER_KEY , SHA256Util.sha256(token),String.valueOf(sysUser.getId()));


        return resp;
    }



    /**
     * 用户分页查询
     */
    @Override
    public PagingInfo<SysUserResp> pageList(SysUserReq req) {
        Page<SysUser> page = new Page<>(req.getPageNum(), req.getPageSize());
        LambdaQueryWrapper<SysUser> query = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(req.getUserName())) {
            query.like(SysUser::getUserName, req.getUserName());
        }
        if (StringUtils.hasText(req.getPhone())) {
            query.like(SysUser::getPhone, req.getPhone());
        }
        // 过滤超级管理员（假设isSuperAdmin字段为1）
        query.ne(SysUser::getIsSuperAdmin, 1);
        IPage<SysUser> userPage = this.page(page, query);
        List<SysUserResp> respList = userPage.getRecords().stream().map(user -> {
            SysUserResp resp = new SysUserResp();
            BeanUtils.copyProperties(user, resp);
            // 查询角色ID列表
            List<Long> roleIdList = userRoleMapper.selectList(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, user.getId()))
                    .stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
            resp.setRoleIdList(roleIdList);

            List<String> roleNames=roleMapper.selectBatchIds(roleIdList).stream().map(SysRole::getName).collect(Collectors.toList());

            resp.setRoleNames(roleNames);
            return resp;
        }).collect(Collectors.toList());
        return PagingInfo.toResponse(respList, userPage.getTotal(), req.getPageNum(), req.getPageSize());

    }

    /**
     * 新增用户
     */
    @Override
    @Transactional
    public void add(SysUserAddReq req) {
        // 用户名唯一性校验
        if (this.count(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, req.getUserName())) > 0) {
            throw new RuntimeException("用户名已存在");
        }
        // 手机号唯一性校验
        if (this.count(new LambdaQueryWrapper<SysUser>().eq(SysUser::getPhone, req.getPhone())) > 0) {
            throw new RuntimeException("手机号已存在");
        }

        Integer isSuperAdmin = RequestHeaderHolder.getSessionUser().getIsSuperAdmin();

        // 密码加密
        String password = DigestUtils.md5DigestAsHex("123456".getBytes());
        SysUser user = new SysUser();
        BeanUtils.copyProperties(req, user);
        user.setPassword(password);
        user.setIsSuperAdmin(0); // 默认非超级管理员
        this.save(user);
        // 分配角色
        if (req.getRoleIdList() != null && !req.getRoleIdList().isEmpty()) {
            List<SysUserRole> userRoles = new ArrayList<>();
            for (Long roleId : req.getRoleIdList()) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(user.getId());
                ur.setRoleId(roleId);
                userRoles.add(ur);
            }
            userRoleMapper.insertBatchSomeColumn(userRoles);
        }
    }

    /**
     * 编辑用户
     */
    @Override
    @Transactional
    public void edit(SysUserEditReq req) {
        SysUser user = this.getById(req.getId());
        if (user == null) throw new RuntimeException("用户不存在");
        if (Objects.equals(user.getIsSuperAdmin(), 1)) throw new RuntimeException("超级管理员不可编辑");
        // 手机号唯一性校验
        if (StringUtils.hasText(req.getPhone()) &&
                this.count(new LambdaQueryWrapper<SysUser>().eq(SysUser::getPhone, req.getPhone()).ne(SysUser::getId, req.getId())) > 0) {
            throw new RuntimeException("手机号已存在");
        }
        BeanUtils.copyProperties(req, user, "id", "password", "isSuperAdmin");
        this.updateById(user);
        // 角色分配
        if (req.getRoleIdList() != null) {
            // 先删除原有
            userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, user.getId()));
            // 再插入新角色
            List<SysUserRole> userRoles = new ArrayList<>();
            for (Long roleId : req.getRoleIdList()) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(user.getId());
                ur.setRoleId(roleId);
                userRoles.add(ur);
            }
            userRoleMapper.insertBatchSomeColumn(userRoles);
        }
    }

    /**
     * 删除用户
     */
    @Override
    @Transactional
    public void delete(Long userId) {
        SysUser user = this.getById(userId);
        if (user == null) throw new RuntimeException("用户不存在");
        if (Objects.equals(user.getIsSuperAdmin(), 1)) throw new RuntimeException("超级管理员不可删除");
        this.removeById(userId);
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
    }



    /**
     * 用户详情
     */
    @Override
    public SysUserResp detail(Long userId) {
        SysUser user = this.getById(userId);
        if (user == null) throw new RuntimeException("用户不存在");
        SysUserResp resp = new SysUserResp();
        BeanUtils.copyProperties(user, resp);
        List<Long> roleIdList = userRoleMapper.selectList(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId))
                .stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
        resp.setRoleIdList(roleIdList);
        return resp;
    }

    /**
     * 分配角色
     */
    @Override
    @Transactional
    public void assignRoles(UserRoleAssignReq req) {
        SysUser user = this.getById(req.getUserId());
        if (user == null) throw new RuntimeException("用户不存在");
        if (Objects.equals(user.getIsSuperAdmin(), 1)) throw new RuntimeException("超级管理员角色不可更改");
        // 先删除原有
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, req.getUserId()));
        // 再插入新角色
        if (req.getRoleIdList() != null && !req.getRoleIdList().isEmpty()) {
            List<SysUserRole> userRoles = new ArrayList<>();
            for (Long roleId : req.getRoleIdList()) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(req.getUserId());
                ur.setRoleId(roleId);
                userRoles.add(ur);
            }
            userRoleMapper.insertBatchSomeColumn(userRoles);
        }
    }

    /**
     * 查询用户已分配角色
     */
    @Override
    public UserRoleResp getUserRoles(Long userId) {
        UserRoleResp resp = new UserRoleResp();
        resp.setUserId(userId);
        List<Long> roleIdList = userRoleMapper.selectList(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId))
                .stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
        resp.setRoleIdList(roleIdList);
        return resp;
    }




    @Override
    @Transactional
    public void resetPassword(ResetPasswordReq req) {

        Integer isSuperAdmin = RequestHeaderHolder.getSessionUser().getIsSuperAdmin();

        if(Objects.equals(isSuperAdmin, Constants.IS_SUPER_ADMIN_FLAG)){
            throw new BaseBizException(BusinessExceptionEnum.FORBID_RESET_OTHER_PASSWORD);
        }

        lambdaUpdate()
                .set(SysUser::getPassword, DigestUtils.md5DigestAsHex(Constants.DEFAULT_PASSWORD.getBytes()))
                .eq(SysUser::getId,req.getUserId())
                .update();
    }
}




