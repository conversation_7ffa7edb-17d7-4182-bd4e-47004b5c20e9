package com.mebotx.xtand.modules.uservip.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import springfox.documentation.annotations.ApiIgnore;

/**
 * 邮件模板预览控制器
 */
@Controller
@RequestMapping("/preview")
@ApiIgnore
public class EmailPreviewController {

    @GetMapping("/verification-code")
    public String previewVerificationCode(@RequestParam(defaultValue = "123456") String code, Model model) {
        model.addAttribute("code", code);
        return "verification-code";
    }

    @GetMapping("/verification-code-simple")
    public String previewVerificationCodeSimple(@RequestParam(defaultValue = "123456") String code, Model model) {
        model.addAttribute("code", code);
        return "verification-code-simple";
    }
}
