package com.mebotx.xtand.modules.uservip.controller;

import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.vo.resp.JsonResult;
import com.mebotx.xtand.common.vo.resp.PagingInfo;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserQueryReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.resp.UserResp;
import com.mebotx.xtand.modules.uservip.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 用户管理接口
 */
@Api(tags = {"用户管理", Constants.ADMIN_API_GROUP})
@RestController
@RequestMapping("/admin/api/user")
public class UserAdminController {

    @Resource
    private UserService userService;

    @ApiOperation("用户分页查询")
    @GetMapping("/list")
    public JsonResult<PagingInfo<UserResp>> list(UserQueryReq req) {
        return JsonResult.buildSuccess(userService.pageList(req));
    }

    @ApiOperation("用户详情")
    @GetMapping("/detail/{id}")
    public JsonResult<UserResp> detail(@PathVariable Long id) {
        return JsonResult.buildSuccess(userService.detail(id));
    }

    @ApiOperation("用户禁用(启用)")
    @PostMapping("/updateStatus")
    public JsonResult<?> updateStatus(@RequestParam Long id, 
                                    @RequestParam @ApiParam(value = "状态 1-启用 0-禁用") Integer status) {
        userService.updateStatus(id, status);
        return JsonResult.buildSuccess();
    }
} 