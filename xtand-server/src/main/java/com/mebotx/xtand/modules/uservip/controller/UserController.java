package com.mebotx.xtand.modules.uservip.controller;

import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.vo.resp.JsonResult;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.LoginReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.SendVerificationCodeReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserEditReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.resp.LoginResp;
import com.mebotx.xtand.modules.uservip.pojo.vo.resp.UserResp;
import com.mebotx.xtand.modules.uservip.service.UserService;
import com.mebotx.xtand.modules.uservip.service.VerificationCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 用户管理接口
 */
@Api(tags = {"用户管理", Constants.APP_API_GROUP})
@RestController
@RequestMapping("/app/api/user")
public class UserController {

    @Resource
    private UserService userService;

    @Resource
    private VerificationCodeService verificationCodeService;

    @ApiOperation("发送登陆验证码")
    @PostMapping("/login/send-verify-code")
    public JsonResult<Boolean> sendVerificationCode(@RequestBody @Valid SendVerificationCodeReq req) {
        Boolean result;
        if (req.getType() == 0) {
            // 发送手机验证码
            result = verificationCodeService.sendSmsCode(req.getTarget());
        } else if (req.getType() == 1) {
            // 发送邮箱验证码
            result = verificationCodeService.sendEmailCode(req.getTarget());
        } else {
            throw new RuntimeException("不支持的验证码类型");
        }
        return JsonResult.buildSuccess(result);
    }

    @ApiOperation("用户登录")
    @PostMapping("/login")
    public JsonResult<LoginResp> login(@RequestBody @Valid LoginReq req) {
        LoginResp resp = userService.login(req);
        return JsonResult.buildSuccess(resp);
    }


    @ApiOperation("编辑用户")
    @PostMapping("/edit")
    public JsonResult<?> edit(@RequestBody @Valid UserEditReq req) {
        userService.edit(req);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("注销用户")
    @PostMapping("/delete/{id}")
    public JsonResult<?> delete(@PathVariable Long id) {
        userService.delete(id);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("用户详情")
    @GetMapping("/detail/{id}")
    public JsonResult<UserResp> detail(@PathVariable Long id) {
        return JsonResult.buildSuccess(userService.detail(id));
    }


    @ApiOperation("更新用户状态")
    @PostMapping("/updateStatus")
    public JsonResult<?> updateStatus(@RequestParam Long id, 
                                    @RequestParam @ApiParam(value = "状态 1-启用 0-禁用") Integer status) {
        userService.updateStatus(id, status);
        return JsonResult.buildSuccess();
    }
} 