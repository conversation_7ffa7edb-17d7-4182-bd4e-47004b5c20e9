package com.mebotx.xtand.modules.uservip.controller;

import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.vo.resp.JsonResult;
import com.mebotx.xtand.common.vo.resp.PagingInfo;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserAddReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserEditReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserQueryReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.resp.UserResp;
import com.mebotx.xtand.modules.uservip.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 用户管理接口
 */
@Api(tags = {"用户管理", Constants.ADMIN_API_GROUP})
@RestController
@RequestMapping("/admin/api/user")
public class UserController {

    @Resource
    private UserService userService;

    @ApiOperation("用户分页查询")
    @GetMapping("/list")
    public JsonResult<PagingInfo<UserResp>> list(UserQueryReq req) {
        return JsonResult.buildSuccess(userService.pageList(req));
    }

    @ApiOperation("新增用户")
    @PostMapping("/add")
    public JsonResult<?> add(@RequestBody @Valid UserAddReq req) {
        userService.add(req);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("编辑用户")
    @PostMapping("/edit")
    public JsonResult<?> edit(@RequestBody @Valid UserEditReq req) {
        userService.edit(req);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("删除用户")
    @PostMapping("/delete/{id}")
    public JsonResult<?> delete(@PathVariable Long id) {
        userService.delete(id);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("用户详情")
    @GetMapping("/detail/{id}")
    public JsonResult<UserResp> detail(@PathVariable Long id) {
        return JsonResult.buildSuccess(userService.detail(id));
    }

    @ApiOperation("启用用户")
    @PostMapping("/enable/{id}")
    public JsonResult<?> enable(@PathVariable Long id) {
        userService.updateStatus(id, 1);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("禁用用户")
    @PostMapping("/disable/{id}")
    public JsonResult<?> disable(@PathVariable Long id) {
        userService.updateStatus(id, 0);
        return JsonResult.buildSuccess();
    }

    @ApiOperation("更新用户状态")
    @PostMapping("/updateStatus")
    public JsonResult<?> updateStatus(@RequestParam Long id, 
                                    @RequestParam @ApiParam(value = "状态 1-启用 0-禁用") Integer status) {
        userService.updateStatus(id, status);
        return JsonResult.buildSuccess();
    }
} 