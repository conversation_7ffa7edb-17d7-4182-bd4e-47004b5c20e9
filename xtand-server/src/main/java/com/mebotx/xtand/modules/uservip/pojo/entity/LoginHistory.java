package com.mebotx.xtand.modules.uservip.pojo.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mebotx.xtand.common.CommonEntity;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* 
* @TableName login_log
*/


@TableName(value = "login_history")
@Data
public class LoginHistory extends CommonEntity implements Serializable{

    /**
    * 主键
    */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * APP的用户ID
     */
    private Long userId;
    /**
    * 用户名
    */
    private String nickName;

    /**
     *  手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;


    /**
    *  登录类型
    */
    private String longType;
    /**
    * 访问IP
    */
    private String clientIp;

    /**
    * 设备信息
    */
    private String deviceInfo;

    /**
    * 登录时间
    */
    private LocalDateTime  loginTime;


    

}
