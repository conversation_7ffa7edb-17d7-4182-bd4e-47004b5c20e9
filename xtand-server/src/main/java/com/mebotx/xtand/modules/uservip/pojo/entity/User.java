package com.mebotx.xtand.modules.uservip.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mebotx.xtand.common.CommonEntity;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户信息表
 * @TableName user
 */
@TableName(value = "user")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User extends CommonEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    private String nickName;

    /**
     * 性别 0-女 1-男
     */
    @ApiModelProperty(value = "性别 0-女 1-男")
    private Long sex;

    /**
     * 身高 (cm)
     */
    @ApiModelProperty(value = "身高 (cm)")
    private Integer height;

    /**
     * 体重 (KG)
     */
    @ApiModelProperty(value = "体重 (KG)")
    private BigDecimal weight;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private LocalDate birthday;

    /**
     * 苹果的唯一标识
     */
    @ApiModelProperty(value = "苹果的唯一标识")
    private String appleid;

    /**
     * 微信唯一标识
     */
    @ApiModelProperty(value = "微信唯一标识")
    private String openid;

    /**
     * 邮箱地址
     */
    @ApiModelProperty(value = "邮箱地址")
    private String email;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 用户地址
     */
    @ApiModelProperty(value = "用户地址")
    private String address;

    /**
     * 头像地址
     */
    @ApiModelProperty(value = "头像地址")
    private String avatar;

    /**
     * 1-启用 0-禁用
     */
    @ApiModelProperty(value = "状态 1-启用 0-禁用")
    private Integer status;


    @ApiModelProperty(value = "最后登陆时间")
    private LocalDateTime lastLoginTime;

    @ApiModelProperty(value = "最后一次登陆IP")

    private String lastLoginIp;

    @ApiModelProperty(value = "最后一次登陆设备")
    private String lastLoginDevice;

    @ApiModelProperty(value = "注册时间")
    private LocalDateTime registrationTime;

} 