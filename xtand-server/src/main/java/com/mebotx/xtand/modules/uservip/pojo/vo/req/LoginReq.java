package com.mebotx.xtand.modules.uservip.pojo.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户登录请求对象
 */
@Data
public class LoginReq implements Serializable {

    @ApiModelProperty(value = "登录类型: M-手机号 E-游戏 W-微信 A-Apple", required = true)
    @NotNull(message = "登录类型不能为空")
    private String loginType;

    @ApiModelProperty(value = "登录凭证(手机号/邮箱/授权码)", required = true)
    @NotBlank(message = "登录凭证不能为空")
    private String content;

    @ApiModelProperty(value = "验证码(手机号/邮箱登录时必填)")
    private String verificationCode;

    @ApiModelProperty(value = "设备信息")
    private String deviceInfo;

    @ApiModelProperty(value = "微信加密数据(微信登录时使用)")
    private String encryptedData;

    @ApiModelProperty(value = "微信初始向量(微信登录时使用)")
    private String iv;
}