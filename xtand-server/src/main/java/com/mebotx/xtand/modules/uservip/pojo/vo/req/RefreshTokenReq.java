package com.mebotx.xtand.modules.uservip.pojo.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 刷新Token请求对象
 */
@Data
public class RefreshTokenReq implements Serializable {

    @ApiModelProperty(value = "刷新令牌", required = true)
    @NotBlank(message = "刷新令牌不能为空")
    private String refreshToken;

    @ApiModelProperty(value = "设备信息")
    private String deviceInfo;
}
