package com.mebotx.xtand.modules.uservip.pojo.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 发送验证码请求对象
 */
@Data
public class SendVerificationCodeReq implements Serializable {

    @ApiModelProperty(value = "验证码类型: 0-手机号 1-邮箱", required = true)
    @NotNull(message = "验证码类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "目标地址(手机号或邮箱)", required = true)
    @NotBlank(message = "目标地址不能为空")
    private String target;
}
