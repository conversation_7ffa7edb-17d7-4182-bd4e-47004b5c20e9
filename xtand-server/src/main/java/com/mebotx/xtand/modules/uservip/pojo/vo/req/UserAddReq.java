package com.mebotx.xtand.modules.uservip.pojo.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 用户新增请求对象
 */
@Data
public class UserAddReq implements Serializable {

    @ApiModelProperty(value = "昵称", required = true)
    @NotBlank(message = "昵称不能为空")
    private String nickName;

    @ApiModelProperty(value = "性别 0-女 1-男")
    private Long sex;

    @ApiModelProperty(value = "身高 (cm)")
    private Integer height;

    @ApiModelProperty(value = "体重 (KG)")
    private BigDecimal weight;

    @ApiModelProperty(value = "出生日期")
    private LocalDate birthday;

    @ApiModelProperty(value = "苹果的唯一标识")
    private String appleid;

    @ApiModelProperty(value = "微信唯一标识")
    private String openid;

    @ApiModelProperty(value = "邮箱地址")
    private String email;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "用户地址")
    private String address;

    @ApiModelProperty(value = "头像地址")
    private String avatar;

    @ApiModelProperty(value = "状态 1-启用 0-禁用")
    private Integer status;
} 