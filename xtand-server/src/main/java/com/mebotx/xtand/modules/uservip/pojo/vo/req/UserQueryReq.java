package com.mebotx.xtand.modules.uservip.pojo.vo.req;

import com.mebotx.xtand.common.vo.req.BasePageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户查询请求对象
 */
@Data
public class UserQueryReq extends BasePageQuery {

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "邮箱地址")
    private String email;

    @ApiModelProperty(value = "状态 1-启用 0-禁用")
    private Integer status;
} 