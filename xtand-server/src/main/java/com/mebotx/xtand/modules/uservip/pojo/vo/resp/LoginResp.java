package com.mebotx.xtand.modules.uservip.pojo.vo.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户登录响应对象
 */
@Data
public class LoginResp implements Serializable {

    @ApiModelProperty(value = "访问令牌")
    private String token;

    @ApiModelProperty(value = "刷新令牌")
    private String refreshToken;

    @ApiModelProperty(value = "令牌过期时间戳")
    private Long expire;

    @ApiModelProperty(value = "刷新令牌过期时间戳")
    private Long refreshExpire;

    @ApiModelProperty(value = "用户信息")
    private UserResp user;

    @ApiModelProperty(value = "是否新用户")
    private Boolean isNewUser;
}
