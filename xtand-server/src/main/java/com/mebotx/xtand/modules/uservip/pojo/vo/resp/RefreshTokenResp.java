package com.mebotx.xtand.modules.uservip.pojo.vo.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 刷新Token响应对象
 */
@Data
public class RefreshTokenResp implements Serializable {

    @ApiModelProperty(value = "新的访问令牌")
    private String token;

    @ApiModelProperty(value = "新的刷新令牌")
    private String refreshToken;

    @ApiModelProperty(value = "访问令牌过期时间戳")
    private Long expire;

    @ApiModelProperty(value = "刷新令牌过期时间戳")
    private Long refreshExpire;
}
