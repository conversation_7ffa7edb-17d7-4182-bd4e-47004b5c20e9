package com.mebotx.xtand.modules.uservip.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mebotx.xtand.common.vo.resp.PagingInfo;
import com.mebotx.xtand.modules.uservip.pojo.entity.User;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserAddReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserEditReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserQueryReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.resp.UserResp;

/**
 * 用户信息服务接口
 */
public interface UserService extends IService<User> {

    /**
     * 用户分页查询
     */
    PagingInfo<UserResp> pageList(UserQueryReq req);

    /**
     * 新增用户
     */
    void add(UserAddReq req);

    /**
     * 编辑用户
     */
    void edit(UserEditReq req);

    /**
     * 删除用户
     */
    void delete(Long id);

    /**
     * 用户详情
     */
    UserResp detail(Long id);

    /**
     * 启用/禁用用户
     */
    void updateStatus(Long id, Integer status);
} 