package com.mebotx.xtand.modules.uservip.service;

/**
 * 验证码服务接口
 */
public interface VerificationCodeService {

    /**
     * 发送手机验证码
     * @param mobile 手机号
     * @return 是否发送成功
     */
    Boolean sendSmsCode(String mobile);

    /**
     * 发送邮箱验证码
     * @param email 邮箱地址
     * @return 是否发送成功
     */
    Boolean sendEmailCode(String email);

    /**
     * 验证手机验证码
     * @param mobile 手机号
     * @param code 验证码
     * @return 是否验证成功
     */
    Boolean verifySmsCode(String mobile, String code);

    /**
     * 验证邮箱验证码
     * @param email 邮箱地址
     * @param code 验证码
     * @return 是否验证成功
     */
    Boolean verifyEmailCode(String email, String code);
}
