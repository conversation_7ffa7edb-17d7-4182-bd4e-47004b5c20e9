package com.mebotx.xtand.modules.uservip.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.constant.LoginTypeEnum;
import com.mebotx.xtand.common.vo.resp.PagingInfo;
import com.mebotx.xtand.modules.uservip.mapper.UserMapper;
import com.mebotx.xtand.modules.uservip.pojo.entity.User;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.LoginReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserEditReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserQueryReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.resp.LoginResp;
import com.mebotx.xtand.modules.uservip.pojo.vo.resp.UserResp;
import com.mebotx.xtand.modules.uservip.service.UserService;
import com.mebotx.xtand.modules.uservip.service.VerificationCodeService;
import com.mebotx.xtand.utils.WXApiUtil;
import com.mebotx.xtand.web.JWTTokenHelper;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.regex.Pattern;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息服务实现类
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Resource
    private VerificationCodeService verificationCodeService;

    @Resource
    private WXApiUtil wxApiUtil;

    @Override
    public PagingInfo<UserResp> pageList(UserQueryReq req) {
        Page<User> page = new Page<>(req.getPageNum(), req.getPageSize());
        LambdaQueryWrapper<User> query = new LambdaQueryWrapper<>();
        
        // 查询条件
        if (StringUtils.hasText(req.getNickName())) {
            query.like(User::getNickName, req.getNickName());
        }
        if (StringUtils.hasText(req.getMobile())) {
            query.like(User::getMobile, req.getMobile());
        }
        if (StringUtils.hasText(req.getEmail())) {
            query.like(User::getEmail, req.getEmail());
        }
        if (req.getStatus() != null) {
            query.eq(User::getStatus, req.getStatus());
        }
        
        // 过滤未删除的记录
        query.eq(User::getIsDeleted, 0);
        
        // 按创建时间倒序
        query.orderByDesc(User::getCreatedTime);
        
        IPage<User> userPage = this.page(page, query);
        List<UserResp> respList = userPage.getRecords().stream().map(user -> {
            UserResp resp = new UserResp();
            BeanUtils.copyProperties(user, resp);
            return resp;
        }).collect(Collectors.toList());
        
        return PagingInfo.toResponse(respList, userPage.getTotal(), req.getPageNum(), req.getPageSize());
    }

    @Override
    @Transactional
    public LoginResp login(LoginReq req) {
        User user = null;
        boolean isNewUser = false;
       LoginTypeEnum loginType = LoginTypeEnum.getByCode(req.getLoginType());

        // 根据登录类型进行验证和用户查找
        switch (loginType) {
            case  LOGIN_WITH_MOBILE: // 手机号登录
                // 验证手机号格式
                if (!Pattern.matches(Constants.REGEX_MOBILE, req.getContent())) {
                    throw new RuntimeException("手机号格式不正确");
                }
                // 验证验证码
                if (!verificationCodeService.verifySmsCode(req.getContent(), req.getVerificationCode())) {
                    throw new RuntimeException("验证码验证失败");
                }
                // 查找用户
                user = this.getOne(new LambdaQueryWrapper<User>()
                        .eq(User::getMobile, req.getContent())
                        .eq(User::getIsDeleted, 0));
                if (user == null) {
                    // 自动注册新用户
                    user = createNewUser(req.getContent(), null, null, null);
                    isNewUser = true;
                }
                break;

            case LOGIN_WITH_EMAIL: // 邮箱登录
                // 验证邮箱格式
                if (!Pattern.matches(Constants.REGEX_EMAIL, req.getContent())) {
                    throw new RuntimeException("邮箱格式不正确");
                }
                // 验证验证码
                if (!verificationCodeService.verifyEmailCode(req.getContent(), req.getVerificationCode())) {
                    throw new RuntimeException("验证码验证失败");
                }
                // 查找用户
                user = this.getOne(new LambdaQueryWrapper<User>()
                        .eq(User::getEmail, req.getContent())
                        .eq(User::getIsDeleted, 0));
                if (user == null) {
                    // 自动注册新用户
                    user = createNewUser(null, req.getContent(), null, null);
                    isNewUser = true;
                }
                break;

            case LOGiN_WITH_WECHAT: // 微信登录
                try {
                    String sessionResult = wxApiUtil.code2Session(req.getContent());
                    JSONObject sessionJson = JSONObject.parseObject(sessionResult);
                    String openid = sessionJson.getString("openid");
                    String sessionKey = sessionJson.getString("session_key");

                    if (StringUtils.hasText(openid)) {
                        // 查找用户
                        user = this.getOne(new LambdaQueryWrapper<User>()
                                .eq(User::getOpenid, openid)
                                .eq(User::getIsDeleted, 0));
                        if (user == null) {
                            // 自动注册新用户
                            user = createNewUser(null, null, openid, null);
                            isNewUser = true;
                        }

                        // 如果有加密数据，尝试获取手机号
                        if (StringUtils.hasText(req.getEncryptedData()) && StringUtils.hasText(req.getIv())) {
                            try {
                                String phoneNumber = wxApiUtil.getUserPhone(sessionKey, req.getEncryptedData(), req.getIv());
                                if (StringUtils.hasText(phoneNumber) && StringUtils.hasText(user.getMobile()) == false) {
                                    user.setMobile(phoneNumber);
                                    this.updateById(user);
                                }
                            } catch (Exception e) {
                                // 获取手机号失败不影响登录
                                log.warn("获取微信手机号失败", e);
                            }
                        }
                    } else {
                        throw new RuntimeException("微信授权失败");
                    }
                } catch (Exception e) {
                    throw new RuntimeException("微信登录失败: " + e.getMessage());
                }
                break;

            case LOGIN_WITH_APPLE: // Apple登录
                // Apple登录需要验证Apple提供的授权码
                // 这里简化处理，实际需要调用Apple的验证接口
                user = this.getOne(new LambdaQueryWrapper<User>()
                        .eq(User::getAppleid, req.getContent())
                        .eq(User::getIsDeleted, 0));
                if (user == null) {
                    // 自动注册新用户
                    user = createNewUser(null, null, null, req.getContent());
                    isNewUser = true;
                }
                break;

            default:
                throw new RuntimeException("不支持的登录类型");
        }

        // 检查用户状态
        if (user.getStatus() == 0) {
            throw new RuntimeException("账户已被禁用");
        }

        // 生成Token
        String token = JWTTokenHelper.generateToken(user.getId(), user.getNickName(), 0, "user");
        String refreshToken = JWTTokenHelper.generateToken(user.getId(), user.getNickName(), 0, "user", 30L * 24 * 60 * 60 * 1000); // 30天

        // 构建响应
        LoginResp resp = new LoginResp();
        resp.setToken(token);
        resp.setRefreshToken(refreshToken);
        resp.setExpire(System.currentTimeMillis() + 2 * 60 * 60 * 1000); // 2小时
        resp.setRefreshExpire(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000); // 30天
        resp.setIsNewUser(isNewUser);

        // 用户信息
        UserResp userResp = new UserResp();
        BeanUtils.copyProperties(user, userResp);
        resp.setUser(userResp);

        return resp;
    }

    /**
     * 创建新用户
     */
    private User createNewUser(String mobile, String email, String openid, String appleid) {
        User user = new User();
        user.setMobile(mobile);
        user.setEmail(email);
        user.setOpenid(openid);
        user.setAppleid(appleid);
        user.setStatus(1); // 默认启用
        user.setIsDeleted(0);

        // 设置默认昵称
        if (StringUtils.hasText(mobile)) {
            user.setNickName("用户" + mobile.substring(mobile.length() - 4));
        } else if (StringUtils.hasText(email)) {
            user.setNickName("用户" + email.substring(0, email.indexOf("@")));
        } else {
            user.setNickName("用户" + System.currentTimeMillis());
        }

        this.save(user);
        return user;
    }

    @Override
    @Transactional
    public void edit(UserEditReq req) {
        User user = this.getById(req.getId());
        if (user == null || user.getIsDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        
        // 手机号唯一性校验
        if (StringUtils.hasText(req.getMobile()) && !req.getMobile().equals(user.getMobile()) && 
            this.count(new LambdaQueryWrapper<User>()
                .eq(User::getMobile, req.getMobile())
                .eq(User::getIsDeleted, 0)
                .ne(User::getId, req.getId())) > 0) {
            throw new RuntimeException("手机号已存在");
        }
        
        // 邮箱唯一性校验
        if (StringUtils.hasText(req.getEmail()) && !req.getEmail().equals(user.getEmail()) && 
            this.count(new LambdaQueryWrapper<User>()
                .eq(User::getEmail, req.getEmail())
                .eq(User::getIsDeleted, 0)
                .ne(User::getId, req.getId())) > 0) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 苹果ID唯一性校验
        if (StringUtils.hasText(req.getAppleid()) && !req.getAppleid().equals(user.getAppleid()) && 
            this.count(new LambdaQueryWrapper<User>()
                .eq(User::getAppleid, req.getAppleid())
                .eq(User::getIsDeleted, 0)
                .ne(User::getId, req.getId())) > 0) {
            throw new RuntimeException("苹果ID已存在");
        }
        
        // 微信openid唯一性校验
        if (StringUtils.hasText(req.getOpenid()) && !req.getOpenid().equals(user.getOpenid()) && 
            this.count(new LambdaQueryWrapper<User>()
                .eq(User::getOpenid, req.getOpenid())
                .eq(User::getIsDeleted, 0)
                .ne(User::getId, req.getId())) > 0) {
            throw new RuntimeException("微信ID已存在");
        }
        
        BeanUtils.copyProperties(req, user, "id");
        this.updateById(user);
    }

    @Override
    @Transactional
    public void delete(Long id) {
        User user = this.getById(id);
        if (user == null || user.getIsDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        
        // 逻辑删除
        user.setIsDeleted(1);
        this.updateById(user);
    }

    @Override
    public UserResp detail(Long id) {
        User user = this.getById(id);
        if (user == null || user.getIsDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        
        UserResp resp = new UserResp();
        BeanUtils.copyProperties(user, resp);
        return resp;
    }

    @Override
    @Transactional
    public void updateStatus(Long id, Integer status) {
        User user = this.getById(id);
        if (user == null || user.getIsDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        
        user.setStatus(status);
        this.updateById(user);
    }
} 