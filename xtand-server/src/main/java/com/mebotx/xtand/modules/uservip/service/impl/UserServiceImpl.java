package com.mebotx.xtand.modules.uservip.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mebotx.xtand.common.vo.resp.PagingInfo;
import com.mebotx.xtand.modules.uservip.mapper.UserMapper;
import com.mebotx.xtand.modules.uservip.pojo.entity.User;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserAddReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserEditReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.UserQueryReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.resp.UserResp;
import com.mebotx.xtand.modules.uservip.service.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息服务实现类
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Override
    public PagingInfo<UserResp> pageList(UserQueryReq req) {
        Page<User> page = new Page<>(req.getPageNum(), req.getPageSize());
        LambdaQueryWrapper<User> query = new LambdaQueryWrapper<>();
        
        // 查询条件
        if (StringUtils.hasText(req.getNickName())) {
            query.like(User::getNickName, req.getNickName());
        }
        if (StringUtils.hasText(req.getMobile())) {
            query.like(User::getMobile, req.getMobile());
        }
        if (StringUtils.hasText(req.getEmail())) {
            query.like(User::getEmail, req.getEmail());
        }
        if (req.getStatus() != null) {
            query.eq(User::getStatus, req.getStatus());
        }
        
        // 过滤未删除的记录
        query.eq(User::getIsDeleted, 0);
        
        // 按创建时间倒序
        query.orderByDesc(User::getCreatedTime);
        
        IPage<User> userPage = this.page(page, query);
        List<UserResp> respList = userPage.getRecords().stream().map(user -> {
            UserResp resp = new UserResp();
            BeanUtils.copyProperties(user, resp);
            return resp;
        }).collect(Collectors.toList());
        
        return PagingInfo.toResponse(respList, userPage.getTotal(), req.getPageNum(), req.getPageSize());
    }

    @Override
    @Transactional
    public void add(UserAddReq req) {
        // 手机号唯一性校验
        if (StringUtils.hasText(req.getMobile()) && 
            this.count(new LambdaQueryWrapper<User>()
                .eq(User::getMobile, req.getMobile())
                .eq(User::getIsDeleted, 0)) > 0) {
            throw new RuntimeException("手机号已存在");
        }
        
        // 邮箱唯一性校验
        if (StringUtils.hasText(req.getEmail()) && 
            this.count(new LambdaQueryWrapper<User>()
                .eq(User::getEmail, req.getEmail())
                .eq(User::getIsDeleted, 0)) > 0) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 苹果ID唯一性校验
        if (StringUtils.hasText(req.getAppleid()) && 
            this.count(new LambdaQueryWrapper<User>()
                .eq(User::getAppleid, req.getAppleid())
                .eq(User::getIsDeleted, 0)) > 0) {
            throw new RuntimeException("苹果ID已存在");
        }
        
        // 微信openid唯一性校验
        if (StringUtils.hasText(req.getOpenid()) && 
            this.count(new LambdaQueryWrapper<User>()
                .eq(User::getOpenid, req.getOpenid())
                .eq(User::getIsDeleted, 0)) > 0) {
            throw new RuntimeException("微信ID已存在");
        }
        
        User user = new User();
        BeanUtils.copyProperties(req, user);
        user.setIsDeleted(0);
        if (user.getStatus() == null) {
            user.setStatus(1); // 默认启用
        }
        this.save(user);
    }

    @Override
    @Transactional
    public void edit(UserEditReq req) {
        User user = this.getById(req.getId());
        if (user == null || user.getIsDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        
        // 手机号唯一性校验
        if (StringUtils.hasText(req.getMobile()) && !req.getMobile().equals(user.getMobile()) && 
            this.count(new LambdaQueryWrapper<User>()
                .eq(User::getMobile, req.getMobile())
                .eq(User::getIsDeleted, 0)
                .ne(User::getId, req.getId())) > 0) {
            throw new RuntimeException("手机号已存在");
        }
        
        // 邮箱唯一性校验
        if (StringUtils.hasText(req.getEmail()) && !req.getEmail().equals(user.getEmail()) && 
            this.count(new LambdaQueryWrapper<User>()
                .eq(User::getEmail, req.getEmail())
                .eq(User::getIsDeleted, 0)
                .ne(User::getId, req.getId())) > 0) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 苹果ID唯一性校验
        if (StringUtils.hasText(req.getAppleid()) && !req.getAppleid().equals(user.getAppleid()) && 
            this.count(new LambdaQueryWrapper<User>()
                .eq(User::getAppleid, req.getAppleid())
                .eq(User::getIsDeleted, 0)
                .ne(User::getId, req.getId())) > 0) {
            throw new RuntimeException("苹果ID已存在");
        }
        
        // 微信openid唯一性校验
        if (StringUtils.hasText(req.getOpenid()) && !req.getOpenid().equals(user.getOpenid()) && 
            this.count(new LambdaQueryWrapper<User>()
                .eq(User::getOpenid, req.getOpenid())
                .eq(User::getIsDeleted, 0)
                .ne(User::getId, req.getId())) > 0) {
            throw new RuntimeException("微信ID已存在");
        }
        
        BeanUtils.copyProperties(req, user, "id");
        this.updateById(user);
    }

    @Override
    @Transactional
    public void delete(Long id) {
        User user = this.getById(id);
        if (user == null || user.getIsDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        
        // 逻辑删除
        user.setIsDeleted(1);
        this.updateById(user);
    }

    @Override
    public UserResp detail(Long id) {
        User user = this.getById(id);
        if (user == null || user.getIsDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        
        UserResp resp = new UserResp();
        BeanUtils.copyProperties(user, resp);
        return resp;
    }

    @Override
    @Transactional
    public void updateStatus(Long id, Integer status) {
        User user = this.getById(id);
        if (user == null || user.getIsDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        
        user.setStatus(status);
        this.updateById(user);
    }
} 