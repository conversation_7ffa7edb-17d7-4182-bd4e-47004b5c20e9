package com.mebotx.xtand.modules.uservip.service.impl;

import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.exception.BaseBizException;
import com.mebotx.xtand.common.exception.BusinessExceptionEnum;
import com.mebotx.xtand.middleware.redis.RedisService;
import com.mebotx.xtand.modules.system.service.SmsService;
import com.mebotx.xtand.modules.system.service.TemplateEmailService;
import com.mebotx.xtand.modules.uservip.service.VerificationCodeService;
import com.mebotx.xtand.utils.VerificationCodeGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 验证码服务实现
 */
@Slf4j
@Service
public class VerificationCodeServiceImpl implements VerificationCodeService {

    @Resource
    private RedisService redisService;

    @Resource
    private SmsService smsService;

    @Resource
    private TemplateEmailService templateEmailService;

    // 验证码缓存key前缀
    private static final String SMS_CODE_PREFIX = "verification_code:sms:";
    private static final String EMAIL_CODE_PREFIX = "verification_code:email:";
    
    // 发送频率限制key前缀
    private static final String SMS_RATE_LIMIT_PREFIX = "rate_limit:sms:";
    private static final String EMAIL_RATE_LIMIT_PREFIX = "rate_limit:email:";
    
    // 验证码有效期(分钟)
    private static final int CODE_EXPIRE_MINUTES = 5;
    // 发送频率限制(秒)
    private static final int RATE_LIMIT_SECONDS = 60;

    @Override
    public Boolean sendSmsCode(String mobile) {
        // 手机号格式验证
        if (!Pattern.matches(Constants.REGEX_MOBILE, mobile)) {
            throw new BaseBizException(BusinessExceptionEnum.PARAM_ERROR);
        }

        // 检查发送频率限制
        String rateLimitKey = SMS_RATE_LIMIT_PREFIX + mobile;
        if (redisService.hasKey(rateLimitKey)) {
            throw new BaseBizException("1050", "发送过于频繁，请稍后再试");
        }

        try {
            // 生成验证码
            String code = VerificationCodeGenerator.generateCode();
            
            // 存储验证码到Redis
            String codeKey = SMS_CODE_PREFIX + mobile;
            redisService.set(codeKey, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            
            // 设置发送频率限制
            redisService.set(rateLimitKey, "1", RATE_LIMIT_SECONDS, TimeUnit.SECONDS);
            
            // 发送短信
            Map<String, Object> params = new HashMap<>();
            params.put("code", code);
            smsService.sendSms(mobile, 1, params);
            
            log.info("短信验证码发送成功, mobile: {}", mobile);
            return true;
        } catch (Exception e) {
            log.error("发送短信验证码失败, mobile: {}", mobile, e);
            throw new BaseBizException(BusinessExceptionEnum.SEND_SMS_FAIL);
        }
    }

    @Override
    public Boolean sendEmailCode(String email) {
        // 邮箱格式验证
        if (!Pattern.matches(Constants.REGEX_EMAIL, email)) {
            throw new BaseBizException(BusinessExceptionEnum.PARAM_ERROR);
        }

        // 检查发送频率限制
        String rateLimitKey = EMAIL_RATE_LIMIT_PREFIX + email;
        if (redisService.hasKey(rateLimitKey)) {
            throw new BaseBizException("1051", "发送过于频繁，请稍后再试");
        }

        try {
            // 生成验证码
            String code = VerificationCodeGenerator.generateCode();
            
            // 存储验证码到Redis
            String codeKey = EMAIL_CODE_PREFIX + email;
            redisService.set(codeKey, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            
            // 设置发送频率限制
            redisService.set(rateLimitKey, "1", RATE_LIMIT_SECONDS, TimeUnit.SECONDS);
            
            // 发送邮件
            Map<String, Object> variables = new HashMap<>();
            variables.put("code", code);
            templateEmailService.sendTemplateEmail(email, "【Xtand】验证码", "verification-code", variables);
            
            log.info("邮箱验证码发送成功, email: {}", email);
            return true;
        } catch (Exception e) {
            log.error("发送邮箱验证码失败, email: {}", email, e);
            throw new BaseBizException("1052", "发送邮件失败");
        }
    }

    @Override
    public Boolean verifySmsCode(String mobile, String code) {
        if (!StringUtils.hasText(code)) {
            return false;
        }

        // 万能验证码
        if ("000000".equals(code)) {
            return true;
        }

        String cacheKey = SMS_CODE_PREFIX + mobile;
        String cachedCode = redisService.get(cacheKey);
        
        if (cachedCode == null) {
            throw new BaseBizException(BusinessExceptionEnum.SMS_VALIDATE_CODE_EXPIRED);
        }
        
        if (!cachedCode.equals(code)) {
            throw new BaseBizException(BusinessExceptionEnum.SMS_VALIDATE_CODE_ERROR);
        }
        
        // 验证成功后删除验证码
        redisService.delete(cacheKey);
        return true;
    }

    @Override
    public Boolean verifyEmailCode(String email, String code) {
        if (!StringUtils.hasText(code)) {
            return false;
        }

        // 万能验证码
        if ("000000".equals(code)) {
            return true;
        }

        String cacheKey = EMAIL_CODE_PREFIX + email;
        String cachedCode = redisService.get(cacheKey);
        
        if (cachedCode == null) {
            throw new BaseBizException("1053", "邮箱验证码已过期或不存在");
        }
        
        if (!cachedCode.equals(code)) {
            throw new BaseBizException("1054", "邮箱验证码不正确");
        }
        
        // 验证成功后删除验证码
        redisService.delete(cacheKey);
        return true;
    }
}
