package com.mebotx.xtand.utils;

import cn.hutool.core.date.DateUtil;
import com.mebotx.xtand.common.exception.BaseBizException;
import com.mebotx.xtand.common.exception.BusinessExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/4/3 21:17
 * @Desc
 */
@Slf4j
public class DateQueryCheckUtil {

    /**
     * 日期格式 yyyy-MM-dd HH:mm:ss
     * 校验前端所传时间的正确性——分页查询
     * @param start 开始时间字符串
     * @param end   结束时间字符串
     * @return      是否校验成功
     */
    public static void verifyStrDate(String start, String end) {

        if (StringUtils.isEmpty(start) && StringUtils.isEmpty(end)) {
            return;
        }

        LocalDateTime startTime = null;
        LocalDateTime endTime = null;

        if (!StringUtils.isEmpty(start)) {
            try {
                startTime = DateUtil.parseLocalDateTime(start);
            } catch (Exception e) {
                log.error("开始日期[{}]无法解析", start, e);
                throw new BaseBizException(BusinessExceptionEnum.PARAM_ERROR, "开始日期格式错误");
            }
        }
        if (!StringUtils.isEmpty(end)) {
            try {
                endTime = DateUtil.parseLocalDateTime(end);
            } catch (Exception e) {
                log.error("结束日期[{}]无法解析", start, e);
                throw new BaseBizException(BusinessExceptionEnum.PARAM_ERROR, "结束日期格式错误");
            }
        }
        if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
            throw new BaseBizException(BusinessExceptionEnum.PARAM_ERROR,"开始日期不能大于结束日期");
        }
    }

}
