package com.mebotx.xtand.utils;


import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class HttpRequestTool {

    // 设置默认超时时间（单位：毫秒）
    private static final int TIMEOUT = 5000;

    /**
     * 发送GET请求
     * @param url 请求的URL
     * @param params 请求参数
     * @param headers 请求头
     * @return 响应结果
     */
    public static String sendGet(String url, Map<String, Object> params, Map<String, String> headers) {

            HttpRequest request = HttpUtil.createGet(url)
                    .setConnectionTimeout(TIMEOUT)
                    .setReadTimeout(TIMEOUT);

            // 设置请求参数
            if (params != null) {
                request.form(params);
            }

            // 设置请求头
            if (headers != null) {
                request.addHeaders(headers);
            }

            HttpResponse response = request.execute();
            return response.body();

    }

    /**
     * 发送POST请求（表单数据）
     * @param url 请求的URL
     * @param formData 表单数据
     * @param headers 请求头
     * @return 响应结果
     */
    public static String sendPostForm(String url, Map<String, Object> formData, Map<String, String> headers) {

            HttpRequest request = HttpUtil.createPost(url)
                    .contentType(ContentType.FORM_URLENCODED.getValue())
                    .setConnectionTimeout(TIMEOUT)
                    .setReadTimeout(TIMEOUT);

            // 设置表单数据
            if (formData != null) {
                request.form(formData);
            }

            // 设置请求头
            if (headers != null) {
                request.addHeaders(headers);
            }

            HttpResponse response = request.execute();
            return response.body();

    }

    /**
     * 发送POST请求（JSON数据）
     * @param url 请求的URL
     * @param jsonData JSON数据
     * @param headers 请求头
     * @return 响应结果
     */
    public static String sendPostJson(String url, String jsonData, Map<String, String> headers) {

            HttpRequest request = HttpUtil.createPost(url)
                    .body(jsonData, ContentType.JSON.getValue())
                    .setConnectionTimeout(TIMEOUT)
                    .setReadTimeout(TIMEOUT);

            // 设置请求头
            if (headers != null) {
                request.addHeaders(headers);
            }

            HttpResponse response = request.execute();
            return response.body();
    }

}
