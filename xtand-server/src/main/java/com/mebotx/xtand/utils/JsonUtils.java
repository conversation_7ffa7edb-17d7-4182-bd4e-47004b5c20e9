package com.mebotx.xtand.utils;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;

import java.util.List;

/**
 /**
 * <AUTHOR>
 * @Date 2025/3/31 17:25
 * @Desc redis集群操作服务类
 */
public class JsonUtils {

    /**
     * json array字符串转集合对象
     * @param text json array字符串源
     * @param clazz 目标类class
     * @return 目标对象
     */
    public static <T> List<T> toList(String text, Class<T> clazz) {
        return JSONArray.parseArray(text, clazz);
    }

    /**
     * json字符串转对象
     * @param text json 字符串源
     * @param clazz 目标类class
     * @return 目标对象
     */
    public static <T> T toObject(String text, Class<T> clazz) {
        return JSON.parseObject(text, clazz);
    }

    /**
     * 对象转json字符串
     * @param object 目标对象实例
     * @return json字符串
     */
    public static String toJSONString(Object object) {
        return JSON.toJSONString(object);
    }

    /**
     * 判断字符串是否为有效的 JSON
     *
     * @param json 待验证的 JSON 字符串
     * @return 如果是有效的 JSON，返回 true，否则返回 false
     */
    public static boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        try {
            JSON.parseObject(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

}
