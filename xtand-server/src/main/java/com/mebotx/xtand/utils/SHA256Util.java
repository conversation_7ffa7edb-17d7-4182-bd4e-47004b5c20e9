package com.mebotx.xtand.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @Date 2025/4/3 22:46
 * @Desc
 */

public class SHA256Util {

    public static String sha256(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    // 字节数组转十六进制工具方法
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        String input = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJleHAiOjE3NDM3NzgwMjYsInVzZXJOYW1lIjoiYWRtaW4iLCJ1c2VySWQiOjJ9.DpEftq5arWy9rGow6FgpJzLoVxqpsZNkjiBRoDdYEn12_94s0l3Fo7emqyEUtOAF67wb35E_KUN9phXVaW_V8w";
        String hash = sha256(input);
        System.out.println("SHA-256 Hash: " + hash);
    }

}
