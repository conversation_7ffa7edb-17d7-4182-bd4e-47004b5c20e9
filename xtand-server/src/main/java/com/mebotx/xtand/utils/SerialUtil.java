package com.mebotx.xtand.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * Author with
 * ClassName:SerialUtil.java
 * Description: 流水号工具类
 */
@Component
@Slf4j
public class SerialUtil {
    /**
     * redis流水号key
     */
    private final static String REDIS_KEY = "votingOrderNo";
    /**
     * 流水号格式
     */
    private final static String FORMAT_CODE = "0000000";

    /**
     * 生成自增流水号
     *
     * @param   stringRedisTemplate 值为String类型
     * @return  Long类型的流水号
     */
    public static Long nextValue(StringRedisTemplate stringRedisTemplate) {

        try {
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String date = formatter.format(now);

            String key = REDIS_KEY + date;
            boolean keyExists = Boolean.TRUE.equals(stringRedisTemplate.hasKey(key));
            if (!keyExists) {
                stringRedisTemplate.opsForValue().increment(key, 0);
                stringRedisTemplate.expire(key, 2, TimeUnit.DAYS);
            }
            DecimalFormat dft = new DecimalFormat(FORMAT_CODE);
            Long count = stringRedisTemplate.opsForValue().increment(key, 1);

            String code = dft.format(count);
            String resultStr = date + code;
            return Long.valueOf(resultStr);
        } catch (Exception e) {
            log.error("Redis生成loan_id失败,开始进行雪花算法生成loan_id....并开始记录错误日志,e:{}",e.getMessage());
            return SnowFlakeUtil.getId();
        }

    }

}
