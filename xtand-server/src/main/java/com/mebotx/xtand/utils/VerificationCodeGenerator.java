package com.mebotx.xtand.utils;

import java.util.Random;

public class VerificationCodeGenerator {

    /**
     * 随机生产6为数字的验证码
     * @return
     */
    public static String generateCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }
}
