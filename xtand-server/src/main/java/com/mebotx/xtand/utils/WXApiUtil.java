package com.mebotx.xtand.utils;

import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.util.crypt.WxMaCryptUtils;
import com.alibaba.fastjson2.JSONObject;

import com.mebotx.xtand.common.constant.WXErrorEnum;
import com.mebotx.xtand.common.exception.BaseBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class WXApiUtil {

    @Value("${wx.appId}")
    private String appId;

    @Value("${wx.appSecret}")
    private String appSecret;

    @Value("${wx.baseUrl}")
    private String baseUrl;


    private HttpRequestTool httpRequestTool;

    /**
     * 获取 session_key 和 openId。
     * @param code
     * @return
     */
    public String code2Session(String code) {

        Map<String, Object> params=new HashMap<>();

        params.put("appid", appId);
        params.put("secret", appSecret);
        params.put("grant_type", "authorization_code");
        params.put("js_code", code);

         String result = httpRequestTool.sendGet(baseUrl+"/sns/jscode2session",params,new HashMap<>());
         log.info("code2Sesson code=>{},sessionKey={}",code,result);
         JSONObject jsonObject = JSONObject.parseObject(result);
         String errNo = String.valueOf(jsonObject.get("errcode"));
         WXErrorEnum e=WXErrorEnum.getByCode(errNo);
         if(e != null){
            throw new BaseBizException(e);
        }
        return result;


    }



    /**
     * 解密获取手机号
     * @param encryptedData
     * @param ivStr
     * @return
     */
    public String getUserPhone(String  sessionKey, String encryptedData, String ivStr){

        WxMaPhoneNumberInfo wxMaPhoneNumberInfo = WxMaPhoneNumberInfo.fromJson(WxMaCryptUtils.decrypt(sessionKey, encryptedData, ivStr));
        return wxMaPhoneNumberInfo.getPhoneNumber();
    }

}
