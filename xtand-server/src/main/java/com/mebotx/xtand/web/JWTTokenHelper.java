package com.mebotx.xtand.web;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.properties.TokenProperties;
import lombok.extern.slf4j.Slf4j;


import javax.servlet.http.HttpServletRequest;
import java.util.Date;

import static com.auth0.jwt.JWT.decode;

/**
 * <AUTHOR>
 * @Date 2025/3/30 23:59
 * @Desc
 */

@Slf4j
public class JWTTokenHelper {

    private static final String USERID_KEY = "userId";
    private static final String USERNAME_KEY = "userName";

    private static  final String REAL_NAME_KEY="realName";

    private static  final String ROLE_KEY="role";

    private static  final String IS_SUPER_ADMIN="isSuperAdmin";


    /**
     * 生成token
     *
     * @param userName
     * @param userId
     * @return
     */
    public static String generateToken(Long userId,String userName,Integer isSuperAdmin,String roles) {

        return generateToken(userId,userName, isSuperAdmin,roles,null);
    }

    public static String generateToken(Long userId,String userName, Integer isSuperAdmin, String roles,Long expiration) {

        return JWT.create()
                .withExpiresAt(generateExpirationDate(expiration))
                .withClaim(USERID_KEY, userId)
                .withClaim(USERNAME_KEY, userName)
                .withClaim(IS_SUPER_ADMIN,isSuperAdmin)
                .withClaim(ROLE_KEY,roles)
                .sign(Algorithm.HMAC512(TokenProperties.getTokenSecret()));

    }

    /**
     * 根据请求获取userName
     *
     * @param request
     * @return
     */
    public static String getUserName(HttpServletRequest request) {
        try {
            String token = request.getHeader(Constants.HEADER_TOKEN);
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(USERNAME_KEY).asString();
        } catch (JWTDecodeException e) {
           log.error("getUserName",e);
        }
        return null;
    }


    public static SessionUser getSessionUser(String token) {
        SessionUser sessionUser=new SessionUser();
        try {
            DecodedJWT jwt = JWT.decode(token);
            sessionUser.setUserId(jwt.getClaim(USERID_KEY).asLong().toString());
            sessionUser.setUserName(jwt.getClaim(USERNAME_KEY).asString());
            sessionUser.setRole(jwt.getClaim(ROLE_KEY).asString());
            sessionUser.setRealName(jwt.getClaim(REAL_NAME_KEY).asString());
            sessionUser.setIsSuperAdmin(jwt.getClaim(IS_SUPER_ADMIN).asInt());
        } catch (JWTDecodeException e) {
            log.error("getSessionUser",e);
        }

        return sessionUser;
    }

    /**
     * 根据token获取userName
     *
     * @param token
     * @return
     */
    public static String getUserName(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(USERNAME_KEY).asString();
        } catch (JWTDecodeException e) {
            log.error("getUserName",e);
        }
        return null;
    }


    public static String getRole(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(ROLE_KEY).asString();
        } catch (JWTDecodeException e) {
            log.error("getRole",e);
            return null;
        }
    }
    /**
     * 根据请求获取userId
     *
     * @param request
     * @return
     */
    public static String getUserId(HttpServletRequest request) {
        try {
            String token = request.getHeader(Constants.HEADER_TOKEN);
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(USERID_KEY).asLong().toString();
        } catch (JWTDecodeException e) {
            log.error("getUserId",e);
            return null;
        }
    }

    /**
     * 根据token获取userId
     *
     * @param token
     * @return
     */
    public static String getUserId(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(USERID_KEY).asLong().toString();
        } catch (JWTDecodeException e) {
            log.error("getUserId",e);
            return null;
        }
    }


    /**
     * token校验是否生效
     *
     * @param token
     */
    public static boolean verifyToken(String token) throws JWTVerificationException {

        try {
            JWT.require(Algorithm.HMAC512(TokenProperties.getTokenSecret()))
                    .withIssuer(TokenProperties.getTokenIssuer())
                    .build()
                    .verify(token);
        } catch (JWTVerificationException e) {
            log.error("verifyToken",e);
            return false;
        }
        return true;

    }

    /**
     * 验证token是否有效（不检查过期时间）
     *
     * @param token
     * @return
     */
    public static boolean verifyTokenWithoutExpiration(String token) {
        try {
            // 只验证签名，不验证过期时间
            JWT.require(Algorithm.HMAC512(TokenProperties.getTokenSecret()))
                    .build()
                    .verify(token.replace("Bearer ", ""));
            return true;
        } catch (Exception e) {
            log.error("verifyTokenWithoutExpiration", e);
            return false;
        }
    }

    /**
     * 检查token是否过期
     *
     * @param token
     * @return true-已过期, false-未过期
     */
    public static boolean isTokenExpired(String token) {
        try {
            Date expiresAt = getExpiresAt(token);
            return expiresAt.before(new Date());
        } catch (Exception e) {
            log.error("isTokenExpired", e);
            return true;
        }
    }

    /**
     * 从token中获取用户ID
     *
     * @param token
     * @return
     */
    public static Long getUserIdFromToken(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(USERID_KEY).asLong();
        } catch (Exception e) {
            log.error("getUserIdFromToken", e);
            return null;
        }
    }

    /**
     * 从token中获取用户名
     *
     * @param token
     * @return
     */
    public static String getUserNameFromToken(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(USERNAME_KEY).asString();
        } catch (Exception e) {
            log.error("getUserNameFromToken", e);
            return null;
        }
    }

    /**
     * 获得token过期时间
     *
     * @param token
     * @return
     */
    public static Date getExpiresAt(String token) {
        return decode(token).getExpiresAt();
    }

    /**
     * 生成token过期时间
     *
     * @return
     */
    private static Date generateExpirationDate(Long expiration) {

        long expirationTime = TokenProperties.getTokenExpireTime();
        if (null != expiration) {
            expirationTime = expiration;
        }

        return new Date(System.currentTimeMillis() + expirationTime);
    }
}
