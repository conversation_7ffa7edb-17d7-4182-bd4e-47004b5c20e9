package com.mebotx.xtand.web;


/**
 * <AUTHOR>
 * @Date 2025/3/30 23:59
 * @Desc
 */
public class RequestHeaderHolder {

    private static final ThreadLocal<SessionUser> sessionUserLocal = new ThreadLocal<>();

    private static final ThreadLocal<String> tokenLocal = new ThreadLocal<>();

    public static SessionUser getSessionUser() {
        return sessionUserLocal.get();
    }

    public static void setSessionUser(SessionUser value) {
        sessionUserLocal.set(value);
    }

    public static void removeSessionUser() {
        sessionUserLocal.remove();
    }

    public static String getToken() {
        return tokenLocal.get();
    }

    public static void setToken(String token) {
        tokenLocal.set(token);
    }

    public static void removeToken() {
        tokenLocal.remove();
    }


    private static final ThreadLocal<Long> adminIdLocal = new ThreadLocal<>();

    public static Long getAdminId(){
        return adminIdLocal.get();
    }

    public static void setAdminId(Long adminId){
         adminIdLocal.set(adminId);
    }
    public static void removeAdminId() {
        adminIdLocal.remove();
    }

}
