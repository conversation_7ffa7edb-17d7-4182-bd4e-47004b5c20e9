package com.mebotx.xtand.web.config;


import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.properties.TokenProperties;
import com.mebotx.xtand.common.properties.WhiteProperties;
import com.mebotx.xtand.middleware.redis.RedisService;
import com.mebotx.xtand.web.interceptor.RequestHeaderInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * 这个注解 @EnableWebMvc 慎用，全面接管SpringMVC
 * 把系统中所有的 WebMvcConfigurer 拿过来。所有功能的定制都是这些 WebMvcConfigurer  合起来一起生效
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {


    private final TokenProperties tokenProperties;

    private final WhiteProperties whiteProperties;

    private final RedisService redisService;

    public WebMvcConfig(TokenProperties tokenProperties,  WhiteProperties whiteProperties,RedisService redisService) {
        this.tokenProperties = tokenProperties;
        this.whiteProperties=whiteProperties;
        this.redisService=redisService;
    }


    @Bean
    public RequestHeaderInterceptor requestHeaderInterceptor() {
        return new RequestHeaderInterceptor(tokenProperties,whiteProperties,redisService);
    }

    /**
     * 1、注入权限拦截器
     *
     * @param registry
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //用户权限拦截器
        registry.addInterceptor( requestHeaderInterceptor() )
                // /** 所有请求都被拦截包括静态资源
                .addPathPatterns("/**")
                // 排除不需要拦截的资源路径
                .excludePathPatterns("/doc.html")
                .excludePathPatterns("/websocket/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/v2/**")
                .excludePathPatterns("/favicon.ico");
        // 书写其他的拦截器
    }

    /**
     * 2、注入参数解析器
     *
     * @param resolvers
     */
    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {

    }


    /**
     * 3、设置资源文件目录
     * 此处关键配置，通过spring应用访问服务器静态资源
     * 上传的图片视频都可以通过此种方式访问
     *
     * @param registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler(Constants.RESOURCE_PREFIX + "/**")
                .addResourceLocations("file:" + "uploadPath" + "/");
    }

    /**
     * 跨域设置
     *
     * @param registry
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**") // 设置允许跨域访问的路径
                .allowedOrigins("*") // 允许跨域请求的源
                .allowedMethods("GET", "POST", "PUT", "DELETE") // 允许请求的方法
                //.allowedHeaders("*") // 允许请求头
                .allowCredentials(true) // 是否允许证书（cookies）
                .maxAge(3600); // 预检请求的有效期，单位为秒
    }
}
