package com.mebotx.xtand.web.interceptor;


import com.mebotx.xtand.common.constant.Constants;
import com.mebotx.xtand.common.exception.BaseBizException;
import com.mebotx.xtand.common.exception.CommonErrorCodeEnum;
import com.mebotx.xtand.common.properties.TokenProperties;
import com.mebotx.xtand.common.properties.WhiteProperties;
import com.mebotx.xtand.middleware.redis.RedisService;
import com.mebotx.xtand.utils.SHA256Util;
import com.mebotx.xtand.web.JWTTokenHelper;
import com.mebotx.xtand.web.RequestHeaderHolder;
import com.mebotx.xtand.web.SessionUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/30 23:59
 * @Desc
 */
@Slf4j
public class RequestHeaderInterceptor implements HandlerInterceptor {

    public RequestHeaderInterceptor(TokenProperties tokenProperties, WhiteProperties whiteProperties, RedisService redisService){
        this.tokenProperties=tokenProperties;
        this.whiteProperties=whiteProperties;
        this.redisService=redisService;
    }


    TokenProperties tokenProperties;

    WhiteProperties whiteProperties;

    RedisService redisService;

    /**
     * 前置拦截 - 获取当前的登录用户，放到当前线程中
     *
     * @param request  请求对象
     * @param response 响应对象
     * @param handler  处理器
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {


        String requestURI = request.getRequestURI();


        log.info("拦截请求路径: {}", requestURI);
        List<String> whiteUrlList=whiteProperties.getUrlList();
        if(whiteUrlList!= null && !whiteUrlList.isEmpty()){

            for (String s : whiteUrlList) {
                String uri = s.trim();
                AntPathMatcher matcher = new AntPathMatcher();
                if (matcher.match(uri, requestURI)) {
                    return true;
                }
            }
        }

        String token;
        // 校验token并在ThreadLocal里面存放当前用户信息
        if (request.getMethod().equals("OPTIONS")) {
            response.setStatus(HttpServletResponse.SC_OK);
            return true;
        } else {
            token = request.getHeader(Constants.HEADER_TOKEN);
        }


         if(StringUtils.isNotBlank(token)){
             token = token.replace("Bearer","").replace(" ","");
         }

        if (StringUtils.isBlank(token)) {
            throw new BaseBizException(CommonErrorCodeEnum.TOKEN_NOT_EXISTS);
        }

        if (JWTTokenHelper.getExpiresAt(token).before(new Date())) {
            throw new BaseBizException(CommonErrorCodeEnum.TOKEN_HAS_EXPIRED);
        }

        if (requestURI.contains("/admin/api")) {
            Boolean hasKey = redisService.hHasKey(Constants.WEB_TOKEN_USER_KEY,SHA256Util.sha256(token));
            if (!hasKey) {
                throw new BaseBizException(CommonErrorCodeEnum.TOKEN_HAS_EXPIRED);
            }
        }
        if (requestURI.contains("/open/api")) {
            Boolean hasKey = redisService.hHasKey(Constants.APP_TOKEN_USER_KEY, SHA256Util.sha256(token));
            if (!hasKey) {
                throw new BaseBizException(CommonErrorCodeEnum.TOKEN_HAS_EXPIRED);
            }
        }

        SessionUser sessionUser=JWTTokenHelper.getSessionUser(token);
        RequestHeaderHolder.setSessionUser(sessionUser);
        RequestHeaderHolder.setToken(token);
        return true;
    }


    /**
     * 清除线程中的用户数据
     *
     * @param request      请求对象
     * @param response     响应对象
     * @param handler      处理器
     * @param modelAndView 视图模型
     * @throws Exception
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        RequestHeaderHolder.removeSessionUser();
        RequestHeaderHolder.removeToken();
    }


}
