
# 设置web访问端口
server:
  port: 8082

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    url: jdbc:mysql://*************:3306/xtand?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&useSSL=false&zeroDateTimeBehavior=convertToNull&serverTimezone=Asia/Shanghai
    username: xtand
    password: cicDSKWESWN8GpYW
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

  redis:
    host: "localhost"
    port: 6379
    password: ""  # 密码中的特殊字符建议用引号包裹
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        max-wait: 5000ms

  mail:
    host: smtp.163.com
    port: 465
    username: "<EMAIL>"
    password: "HMh6JydAdCrU9ndN"  # 客户端授权密码
    protocol: smtp
    properties:
      mail.smtp:
        auth: true
        ssl:
          enable: true
          protocols: TLSv1.2
        connection-timeout: 5000
        timeout: 5000
        write timeout: 5000
    mail.debug: true

# 配置mybatis plus
mybatis-plus:
  configuration:
    #驼峰命名规则
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath:mapper/*.xml

minio:
  endpoint: "https://file.liuyl.xin"
  access-key: QTvu3ecasId9KqXIs7hB
  secret-key: OgYcwuVQegTnMsBb9oL7auVEgXCE0QOvxcOt2s2W
  bucket-name: test
  secure: false



token:
  expireTime: 86400000 # 一天时间 24*60*60*1000
  secret: votingEncrypt
  issuer: voting

swagger:
  description: 智能髌骨带APP系统
  title: 智能髌骨带APP系统
  scan-base-package: com.mebotx.xtand.modules
  enable: true

thread:
  pool:
    core-pool-size: 5
    max-pool-size: 10
    queue-capacity: 50
    keep-alive-seconds: 60


##白名单接口列表
white:
  url-list:
    - /admin/api/sysUser/login
    - /app/api/user/login
    - /app/api/user/login/send-verify-code
    - /preview/**
    - /api/files/**
    - /api/sms/**
    - /app/api/users/authentication
    - /app/api/files/upload

wx:
  appId: wx4f88011a3184253d
  appSecret:  d57cfbc765b9a4e59e5bb0f894953e82
  baseUrl: https://api.weixin.qq.com

