<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<!-- 定义日志文件的保存路径 -->
	<property name="LOG_FILE_DIR" value="logs"/>
	<property name="LOG_FILE_NAME" value="app.log"/>

	<!-- 控制台输出 -->
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
		</encoder>
	</appender>

	<!-- 文件输出，使用 RollingFileAppender 进行日志滚动和归档 -->
	<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_FILE_DIR}/${LOG_FILE_NAME}</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- 归档文件的命名规则，按天进行归档，文件名包含日期和序号 -->
			<fileNamePattern>${LOG_FILE_DIR}/archived/${LOG_FILE_NAME}.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
			<!-- 单个日志文件最大大小为 20MB -->
			<maxFileSize>20MB</maxFileSize>
			<!-- 保留最近 10 个归档文件 -->
			<maxHistory>50</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
		</encoder>
	</appender>

	<!-- 设置根日志级别，同时将日志输出到控制台和文件 -->
	<root level="info"  additivity="true">
		<appender-ref ref="STDOUT"/>
		<appender-ref ref="FILE"/>
	</root>
</configuration>
