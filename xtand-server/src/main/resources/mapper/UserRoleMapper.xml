<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mebotx.xtand.modules.system.mapper.UserRoleMapper">
    <insert id="insertBatchSomeColumn">
        insert into sys_user_role (user_id, role_id, created_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.roleId}, now())
        </foreach>
    </insert>
</mapper> 