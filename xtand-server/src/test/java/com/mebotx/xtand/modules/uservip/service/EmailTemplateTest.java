package com.mebotx.xtand.modules.uservip.service;

import com.mebotx.xtand.modules.system.service.TemplateEmailService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 邮件模板测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class EmailTemplateTest {

    @Resource
    private TemplateEmailService templateEmailService;

    @Test
    public void testSendVerificationCodeEmail() {
        // 测试发送验证码邮件
        Map<String, Object> variables = new HashMap<>();
        variables.put("code", "123456");
        
        // 注意：这里需要替换为您的真实邮箱地址进行测试
        String testEmail = "<EMAIL>";
        
        try {
            templateEmailService.sendTemplateEmail(
                testEmail, 
                "【Xtand】验证码", 
                "verification-code", 
                variables
            );
            System.out.println("邮件发送成功到: " + testEmail);
        } catch (Exception e) {
            System.err.println("邮件发送失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testSendSimpleVerificationCodeEmail() {
        // 测试发送简化版验证码邮件
        Map<String, Object> variables = new HashMap<>();
        variables.put("code", "654321");
        
        // 注意：这里需要替换为您的真实邮箱地址进行测试
        String testEmail = "<EMAIL>";
        
        try {
            templateEmailService.sendTemplateEmail(
                testEmail, 
                "【Xtand】验证码（简化版）", 
                "verification-code-simple", 
                variables
            );
            System.out.println("简化版邮件发送成功到: " + testEmail);
        } catch (Exception e) {
            System.err.println("简化版邮件发送失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
