package com.mebotx.xtand.modules.uservip.service;

import com.mebotx.xtand.modules.uservip.pojo.vo.req.LoginReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.req.RefreshTokenReq;
import com.mebotx.xtand.modules.uservip.pojo.vo.resp.LoginResp;
import com.mebotx.xtand.modules.uservip.pojo.vo.resp.RefreshTokenResp;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 登录服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class LoginServiceTest {

    @Resource
    private UserService userService;

    @Resource
    private VerificationCodeService verificationCodeService;

    @Test
    public void testSendSmsCode() {
        // 测试发送手机验证码
        String mobile = "13800138000";
        Boolean result = verificationCodeService.sendSmsCode(mobile);
        assertTrue(result);
    }

    @Test
    public void testVerifySmsCode() {
        // 测试万能验证码
        String mobile = "13800138000";
        String code = "000000";
        Boolean result = verificationCodeService.verifySmsCode(mobile, code);
        assertTrue(result);
    }

    @Test
    public void testMobileLogin() {
        // 测试手机号登录
        LoginReq req = new LoginReq();
        req.setLoginType(0);
        req.setContent("13800138000");
        req.setVerificationCode("000000");
        req.setDeviceInfo("Test Device");

        LoginResp resp = userService.login(req);
        
        assertNotNull(resp);
        assertNotNull(resp.getToken());
        assertNotNull(resp.getUser());
        assertTrue(resp.getIsNewUser()); // 第一次登录应该是新用户
    }

    @Test
    public void testEmailLogin() {
        // 测试邮箱登录
        LoginReq req = new LoginReq();
        req.setLoginType(1);
        req.setContent("<EMAIL>");
        req.setVerificationCode("000000");
        req.setDeviceInfo("Test Device");

        LoginResp resp = userService.login(req);
        
        assertNotNull(resp);
        assertNotNull(resp.getToken());
        assertNotNull(resp.getUser());
        assertTrue(resp.getIsNewUser()); // 第一次登录应该是新用户
    }

    @Test
    public void testInvalidLoginType() {
        // 测试无效的登录类型
        LoginReq req = new LoginReq();
        req.setLoginType(99);
        req.setContent("test");
        req.setVerificationCode("000000");

        assertThrows(RuntimeException.class, () -> {
            userService.login(req);
        });
    }

    @Test
    public void testInvalidMobileFormat() {
        // 测试无效的手机号格式
        LoginReq req = new LoginReq();
        req.setLoginType(0);
        req.setContent("invalid_mobile");
        req.setVerificationCode("000000");

        assertThrows(RuntimeException.class, () -> {
            userService.login(req);
        });
    }

    @Test
    public void testInvalidEmailFormat() {
        // 测试无效的邮箱格式
        LoginReq req = new LoginReq();
        req.setLoginType(1);
        req.setContent("invalid_email");
        req.setVerificationCode("000000");

        assertThrows(RuntimeException.class, () -> {
            userService.login(req);
        });
    }

    @Test
    public void testRefreshToken() {
        // 先登录获取refreshToken
        LoginReq loginReq = new LoginReq();
        loginReq.setLoginType(0);
        loginReq.setContent("13800138001");
        loginReq.setVerificationCode("000000");
        loginReq.setDeviceInfo("Test Device");

        LoginResp loginResp = userService.login(loginReq);
        assertNotNull(loginResp.getRefreshToken());

        // 使用refreshToken刷新token
        RefreshTokenReq refreshReq = new RefreshTokenReq();
        refreshReq.setRefreshToken(loginResp.getRefreshToken());
        refreshReq.setDeviceInfo("Test Device");

        RefreshTokenResp refreshResp = userService.refreshToken(refreshReq);

        assertNotNull(refreshResp);
        assertNotNull(refreshResp.getToken());
        assertNotNull(refreshResp.getRefreshToken());
        assertTrue(refreshResp.getExpire() > System.currentTimeMillis());
        assertTrue(refreshResp.getRefreshExpire() > System.currentTimeMillis());

        // 新的token应该与原来的不同
        assertNotEquals(loginResp.getToken(), refreshResp.getToken());
    }

    @Test
    public void testRefreshTokenWithInvalidToken() {
        // 测试无效的refreshToken
        RefreshTokenReq req = new RefreshTokenReq();
        req.setRefreshToken("invalid_token");
        req.setDeviceInfo("Test Device");

        assertThrows(RuntimeException.class, () -> {
            userService.refreshToken(req);
        });
    }

    @Test
    public void testRefreshTokenWithEmptyToken() {
        // 测试空的refreshToken
        RefreshTokenReq req = new RefreshTokenReq();
        req.setRefreshToken("");
        req.setDeviceInfo("Test Device");

        assertThrows(RuntimeException.class, () -> {
            userService.refreshToken(req);
        });
    }
}
