spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
  h2:
    console:
      enabled: true
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  redis:
    host: "localhost"
    port: 6379
    password: ""

# 测试环境配置
logging:
  level:
    com.mebotx.xtand: DEBUG
    
# 禁用一些不需要的功能
management:
  endpoints:
    enabled-by-default: false
