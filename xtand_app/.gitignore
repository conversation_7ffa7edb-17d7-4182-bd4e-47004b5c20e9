# Miscellaneous
.DS_Store
.env
.flutter-plugins
.flutter-plugins-dependencies
.metadata
.packages
.idea/

# Build artifacts
/build/
/.dart_tool/
/ios/.symlinks/
/ios/Flutter/Generated.xcconfig
/android/.gradle/
/android/local.properties
/android/key.properties
/android/**/*.iml
/android/build/
/android/app/build/

# IDE-specific files
.vscode/
*.iml
*.ipr
*.iws

# Generated files
/lib/generated/

# Crashlytics
/ios/Runner/GoogleService-Info.plist
/android/app/google-services.json

# macOS specific
.DS_Store

# Android specific
local.properties

# iOS specific
.symlinks/

# Flutter specific
.flutter-plugins
.flutter-plugins-dependencies
.metadata
.packages

# VS Code specific
.vscode/

# Other
*.log
*.lock
*.bak
*~

# For Flutter apps, the following should be ignored
# if you are using the default Flutter project structure.
# If you have custom build directories, add them here.

# Android
android/app/src/main/assets/flutter_assets/
android/app/build/

# iOS
ios/Flutter/App.framework/
ios/Flutter/Flutter.framework/
ios/Flutter/flutter_assets/
ios/Runner/GeneratedPluginRegistrant.h
ios/Runner/GeneratedPluginRegistrant.m

# Web
web/index.html
web/main.dart.js
web/main.dart.js.map
web/manifest.json
web/flutter_service_worker.js
web/version.json

# Windows
windows/flutter/generated_plugin_registrant.cc
windows/flutter/generated_plugin_registrant.h
windows/flutter/generated_plugin_registrant.cpp
windows/flutter/flutter_wrapper_app.vcxproj
windows/flutter/flutter_wrapper_app.vcxproj.filters
windows/runner/flutter_windows.dll
windows/runner/flutter_windows.dll.lib
windows/runner/flutter_windows.exp
windows/runner/flutter_windows.lib
windows/runner/flutter_windows.pdb
windows/runner/Runner.rc
windows/runner/Runner.vcxproj
windows/runner/Runner.vcxproj.filters
windows/runner/Runner.dir/
windows/runner/x64/

# Linux
linux/flutter/generated_plugin_registrant.cc
linux/flutter/generated_plugin_registrant.h
linux/flutter/generated_plugin_registrant.cpp
linux/flutter/flutter_linux.so
linux/flutter/flutter_linux.so.debug
linux/flutter/flutter_linux.so.dwo
linux/flutter/flutter_linux.so.dwp
linux/flutter/flutter_linux.so.map
linux/flutter/flutter_linux.so.sym
linux/runner/generated_plugin_registrant.cc
linux/runner/generated_plugin_registrant.h
linux/runner/generated_plugin_registrant.cpp
linux/runner/main.cc
linux/runner/CMakeLists.txt
linux/runner/build/

# macOS
macos/Flutter/GeneratedPluginRegistrant.h
macos/Flutter/GeneratedPluginRegistrant.m
macos/Flutter/ephemeral/
macos/Runner/GeneratedPluginRegistrant.h
macos/Runner/GeneratedPluginRegistrant.m

# Other generated files
*.g.dart
*.freezed.dart

# Dependency caching
.dart_tool/
.packages
.flutter-plugins
.flutter-plugins-dependencies

# Local configuration
.env
.vscode/

# Test coverage
/coverage/

# VS Code
.vscode/

# Android Studio / IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Gradle
.gradle/
build/

# Xcode
*.xcodeproj/
*.xcworkspace/

# Swift Package Manager
.swiftpm/

# Carthage
Carthage/Build/

# CocoaPods
Pods/

# Fastlane
fastlane/report.xml
fastlane/screenshots/

# Other
*.log
*.lock
*.bak
*~