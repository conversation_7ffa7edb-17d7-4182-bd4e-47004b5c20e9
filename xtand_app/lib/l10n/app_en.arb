{"@@locale": "en", "appTitle": "Xtand", "login": "<PERSON><PERSON>", "enterPhoneNumber": "Please enter your phone number", "enterVerificationCode": "Please enter verification code", "getVerificationCode": "Get Code", "loginRegister": "Login/Register", "agree": "Agree", "userAgreement": "User Agreement", "privacyPolicy": "Privacy Policy", "orLoginWith": "Or login with", "verificationCodeSent": "Verification code sent", "phoneLogin": "Phone", "emailLogin": "Email", "enterEmail": "Please enter your email", "enterPassword": "Please enter your password", "pleaseAgreeToTerms": "Please agree to the terms and privacy policy", "home": "Home", "data": "Data", "profile": "Profile", "todoHomeContent": "TODO: Home content", "todoDataContent": "TODO: Data content", "todoProfileContent": "TODO: Profile content", "homeDescription": "This will display smart patella band connection status, real-time data, etc.", "dataDescription": "This will display exercise data, statistical charts, etc.", "profileDescription": "This will display user information, settings, etc.", "device": "<PERSON><PERSON>", "deviceConnection": "Device Connection", "deviceList": "Device List", "scanDevices": "<PERSON>an <PERSON>ces", "stopScan": "Stop Scan", "connectDevice": "Connect Device", "disconnectDevice": "Disconnect Device", "connected": "Connected", "disconnected": "Disconnected", "connecting": "Connecting", "disconnecting": "Disconnecting", "connectionError": "Connection Error", "noDevicesFound": "No Devices Found", "bluetoothDisabled": "Bluetooth Disabled", "bluetoothUnavailable": "Bluetooth Unavailable", "deviceStatus": "Device Status", "batteryLevel": "Battery Level", "signalStrength": "Signal Strength", "pressure": "Pressure", "exerciseData": "Exercise Data", "impactForce": "Impact Force", "dampingForce": "Damping Force", "jumpCount": "Jump Count", "impactCount": "Impact Count", "jumpHeight": "Jump Height", "exerciseState": "Exercise State", "standing": "Standing", "walking": "Walking", "running": "Running", "jumping": "Jumping", "generation1": "Generation 1", "generation2": "Generation 2", "excellent": "Excellent", "good": "Good", "fair": "Fair", "poor": "Poor", "retry": "Retry", "cancel": "Cancel", "confirm": "Confirm"}