// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'XTand App';

  @override
  String get login => 'Login';

  @override
  String get enterPhoneNumber => 'Please enter your phone number';

  @override
  String get enterVerificationCode => 'Please enter verification code';

  @override
  String get getVerificationCode => 'Get Code';

  @override
  String get loginRegister => 'Login/Register';

  @override
  String get agree => 'Agree';

  @override
  String get userAgreement => 'User Agreement';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get orLoginWith => 'Or login with';

  @override
  String get verificationCodeSent => 'Verification code sent';

  @override
  String get home => 'Home';

  @override
  String get data => 'Data';

  @override
  String get profile => 'Profile';

  @override
  String get todoHomeContent => 'TODO: Home content';

  @override
  String get todoDataContent => 'TODO: Data content';

  @override
  String get todoProfileContent => 'TODO: Profile content';

  @override
  String get homeDescription =>
      'This will display smart patella band connection status, real-time data, etc.';

  @override
  String get dataDescription =>
      'This will display exercise data, statistical charts, etc.';

  @override
  String get profileDescription =>
      'This will display user information, settings, etc.';
}
