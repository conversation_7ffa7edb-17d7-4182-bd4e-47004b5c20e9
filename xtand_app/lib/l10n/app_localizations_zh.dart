// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => 'XTand App';

  @override
  String get login => '登录';

  @override
  String get enterPhoneNumber => '请输入手机号码';

  @override
  String get enterVerificationCode => '请输入短信验证码';

  @override
  String get getVerificationCode => '获取验证码';

  @override
  String get loginRegister => '登录/注册';

  @override
  String get agree => '同意';

  @override
  String get userAgreement => '用户协议';

  @override
  String get privacyPolicy => '隐私政策';

  @override
  String get orLoginWith => '或通过以下方式登录';

  @override
  String get verificationCodeSent => '验证码已发送';

  @override
  String get home => '首页';

  @override
  String get data => '数据';

  @override
  String get profile => '我的';

  @override
  String get todoHomeContent => 'TODO: 首页内容';

  @override
  String get todoDataContent => 'TODO: 数据页面内容';

  @override
  String get todoProfileContent => 'TODO: 我的页面内容';

  @override
  String get homeDescription => '这里将显示智能髌骨带的连接状态、实时数据等';

  @override
  String get dataDescription => '这里将显示运动数据、统计图表等';

  @override
  String get profileDescription => '这里将显示用户信息、设置选项等';
}
