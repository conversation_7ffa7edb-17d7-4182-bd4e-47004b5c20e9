import 'package:flutter/material.dart';
import 'package:xtand_app/src/features/auth/login_page.dart';
import 'package:xtand_app/src/features/main_navigation.dart';
import 'package:xtand_app/src/features/device/device_list_page.dart';

class AppRoutes {
  static const String login = '/';
  static const String main = '/main';
  static const String deviceList = '/device-list';

  static Map<String, WidgetBuilder> get routes => {
    login: (context) => const LoginPage(),
    main: (context) => const MainNavigation(),
    deviceList: (context) => const DeviceListPage(),
  };
}
