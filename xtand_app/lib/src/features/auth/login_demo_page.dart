import 'package:flutter/material.dart';
import 'package:xtand_app/generated/app_localizations.dart';
import 'package:xtand_app/src/services/region_service.dart';
import 'package:xtand_app/src/features/auth/login_page.dart';

/// 登录页面演示，用于展示不同地区的登录界面
class LoginDemoPage extends StatefulWidget {
  const LoginDemoPage({super.key});

  @override
  State<LoginDemoPage> createState() => _LoginDemoPageState();
}

class _LoginDemoPageState extends State<LoginDemoPage> {
  bool _isInChina = true;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text('登录页面演示'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          // 地区切换按钮
          TextButton(
            onPressed: _toggleRegion,
            child: Text(
              _isInChina ? '切换到海外' : '切换到中国',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // 当前地区显示
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: _isInChina ? Colors.red.shade50 : Colors.blue.shade50,
            child: Column(
              children: [
                Text(
                  '当前地区: ${_isInChina ? "中国" : "海外"}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _getRegionDescription(),
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          // 登录页面预览
          Expanded(
            child: _buildLoginPreview(),
          ),
        ],
      ),
    );
  }

  String _getRegionDescription() {
    if (_isInChina) {
      return '中国地区支持：手机号登录、邮箱登录、微信登录、Apple ID登录';
    } else {
      return '海外地区支持：邮箱登录、Apple ID登录\n（手机号和微信登录不可用）';
    }
  }

  void _toggleRegion() {
    setState(() {
      _isInChina = !_isInChina;
    });
    
    // 更新地区服务的设置
    RegionService.instance.setRegion(isInChina: _isInChina);
  }

  Widget _buildLoginPreview() {
    // 这里创建一个简化的登录页面预览
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: const LoginPage(),
      ),
    );
  }
}
