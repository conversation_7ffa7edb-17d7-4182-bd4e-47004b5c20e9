import 'package:flutter/material.dart';
import 'package:xtand_app/generated/app_localizations.dart';
import 'package:xtand_app/src/services/region_service.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _codeController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isAgreed = false;
  LoginMethod _currentMethod = LoginMethod.phone;
  bool _isInChina = true; // 默认为中国，实际应通过地理位置或IP检测
  bool _isPasswordVisible = false;

  @override
  void initState() {
    super.initState();
    _detectRegion();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _emailController.dispose();
    _codeController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // 检测用户所在地区
  void _detectRegion() async {
    try {
      final regionService = RegionService.instance;
      final isInChina = await regionService.isInChina();
      final defaultMethod = await regionService.getDefaultLoginMethod();

      setState(() {
        _isInChina = isInChina;
        _currentMethod = defaultMethod;
      });
    } catch (e) {
      // 出错时使用默认设置
      setState(() {
        _isInChina = false;
        _currentMethod = LoginMethod.email;
      });
    }
  }

  void _login() {
    if (!_isAgreed) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.of(context)!.pleaseAgreeToTerms)),
      );
      return;
    }

    // 根据登录方式进行不同的验证
    switch (_currentMethod) {
      case LoginMethod.phone:
        _loginWithPhone();
        break;
      case LoginMethod.email:
        _loginWithEmail();
        break;
      case LoginMethod.wechat:
        _loginWithWechat();
        break;
      case LoginMethod.apple:
        _loginWithApple();
        break;
    }
  }

  void _loginWithPhone() {
    // TODO: 手机号登录逻辑
    Navigator.of(context).pushReplacementNamed('/main');
  }

  void _loginWithEmail() {
    // TODO: 邮箱登录逻辑
    Navigator.of(context).pushReplacementNamed('/main');
  }

  void _loginWithWechat() {
    // TODO: 微信登录逻辑
    Navigator.of(context).pushReplacementNamed('/main');
  }

  void _loginWithApple() {
    // TODO: Apple ID登录逻辑
    Navigator.of(context).pushReplacementNamed('/main');
  }

  void _getVerificationCode() {
    // TODO: 获取验证码逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(AppLocalizations.of(context)!.verificationCodeSent)),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              const SizedBox(height: 80),
              // 登录标题
              Text(
                l10n.login,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 40),
              // 登录方式选择器（仅在中国显示）
              if (_isInChina) _buildLoginMethodSelector(l10n),
              const SizedBox(height: 40),
              // 根据选择的登录方式显示不同的输入框
              _buildLoginInputs(l10n),
              const SizedBox(height: 60),
              // 登录按钮
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: _login,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: Text(
                    l10n.loginRegister,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 30),
              // 用户协议
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Checkbox(
                    value: _isAgreed,
                    onChanged: (value) {
                      setState(() {
                        _isAgreed = value ?? false;
                      });
                    },
                  ),
                  Text(
                    l10n.agree,
                    style: const TextStyle(color: Colors.grey),
                  ),
                  GestureDetector(
                    onTap: () {
                      // TODO: 跳转到用户协议页面
                    },
                    child: Text(
                      l10n.userAgreement,
                      style: const TextStyle(
                        color: Colors.blue,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                  const Text(
                    '/',
                    style: TextStyle(color: Colors.grey),
                  ),
                  GestureDetector(
                    onTap: () {
                      // TODO: 跳转到隐私政策页面
                    },
                    child: Text(
                      l10n.privacyPolicy,
                      style: const TextStyle(
                        color: Colors.blue,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ),
              const Spacer(),
              // 分割线和提示文字
              Row(
                children: [
                  const Expanded(child: Divider()),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      l10n.orLoginWith,
                      style: const TextStyle(color: Colors.grey),
                    ),
                  ),
                  const Expanded(child: Divider()),
                ],
              ),
              const SizedBox(height: 30),
              // 第三方登录图标
              _buildSocialLoginButtons(l10n),
              const SizedBox(height: 50),
            ],
          ),
        ),
      ),
    );
  }

  // 构建登录方式选择器
  Widget _buildLoginMethodSelector(AppLocalizations l10n) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildMethodTab(l10n.phoneLogin, LoginMethod.phone),
        _buildMethodTab(l10n.emailLogin, LoginMethod.email),
      ],
    );
  }

  Widget _buildMethodTab(String title, LoginMethod method) {
    final isSelected = _currentMethod == method;
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentMethod = method;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey,
          ),
        ),
        child: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  // 构建登录输入框
  Widget _buildLoginInputs(AppLocalizations l10n) {
    switch (_currentMethod) {
      case LoginMethod.phone:
        return _buildPhoneInputs(l10n);
      case LoginMethod.email:
        return _buildEmailInputs(l10n);
      default:
        return _buildPhoneInputs(l10n);
    }
  }

  Widget _buildPhoneInputs(AppLocalizations l10n) {
    return Column(
      children: [
        // 手机号输入框
        TextField(
          controller: _phoneController,
          keyboardType: TextInputType.phone,
          decoration: InputDecoration(
            hintText: l10n.enterPhoneNumber,
            hintStyle: const TextStyle(color: Colors.grey),
            border: const UnderlineInputBorder(),
            enabledBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.grey),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.blue),
            ),
          ),
        ),
        const SizedBox(height: 30),
        // 验证码输入框
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _codeController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: l10n.enterVerificationCode,
                  hintStyle: const TextStyle(color: Colors.grey),
                  border: const UnderlineInputBorder(),
                  enabledBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey),
                  ),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.blue),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            GestureDetector(
              onTap: _getVerificationCode,
              child: Text(
                l10n.getVerificationCode,
                style: const TextStyle(
                  color: Colors.blue,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEmailInputs(AppLocalizations l10n) {
    return Column(
      children: [
        // 邮箱输入框
        TextField(
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          decoration: InputDecoration(
            hintText: l10n.enterEmail,
            hintStyle: const TextStyle(color: Colors.grey),
            border: const UnderlineInputBorder(),
            enabledBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.grey),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.blue),
            ),
          ),
        ),
        const SizedBox(height: 30),
        // 密码输入框
        TextField(
          controller: _passwordController,
          obscureText: !_isPasswordVisible,
          decoration: InputDecoration(
            hintText: l10n.enterPassword,
            hintStyle: const TextStyle(color: Colors.grey),
            border: const UnderlineInputBorder(),
            enabledBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.grey),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.blue),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                color: Colors.grey,
              ),
              onPressed: () {
                setState(() {
                  _isPasswordVisible = !_isPasswordVisible;
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  // 构建第三方登录按钮组
  Widget _buildSocialLoginButtons(AppLocalizations l10n) {
    List<Widget> buttons = [];

    // Apple ID 登录（全球可用）
    buttons.add(_buildSocialLoginButton(
      Icons.apple,
      Colors.black,
      _loginWithApple,
    ));

    // 邮箱登录（全球可用）
    if (_currentMethod != LoginMethod.email) {
      buttons.add(_buildSocialLoginButton(
        Icons.email,
        Colors.grey,
        () {
          setState(() {
            _currentMethod = LoginMethod.email;
          });
        },
      ));
    }

    // 微信登录（仅中国可用）
    if (_isInChina) {
      buttons.add(_buildSocialLoginButton(
        Icons.wechat,
        Colors.green,
        _loginWithWechat,
      ));
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: buttons,
    );
  }

  Widget _buildSocialLoginButton(IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 30,
        ),
      ),
    );
  }
}