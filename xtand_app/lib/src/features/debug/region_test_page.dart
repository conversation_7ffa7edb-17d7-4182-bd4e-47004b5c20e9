import 'package:flutter/material.dart';
import 'package:xtand_app/src/services/region_service.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

/// 地区检测测试页面 - 用于调试和验证不同检测方法
class RegionTestPage extends StatefulWidget {
  const RegionTestPage({super.key});

  @override
  State<RegionTestPage> createState() => _RegionTestPageState();
}

class _RegionTestPageState extends State<RegionTestPage> {
  Map<String, dynamic> _testResults = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _runAllTests();
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isLoading = true;
      _testResults.clear();
    });

    // 测试1: 系统语言检测
    await _testSystemLocale();
    
    // 测试2: 时区检测
    await _testTimeZone();
    
    // 测试3: IP地址检测
    await _testIPDetection();
    
    // 测试4: 网络连通性检测
    await _testConnectivity();
    
    // 测试5: 综合检测
    await _testComprehensive();

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testSystemLocale() async {
    try {
      final locale = WidgetsBinding.instance.platformDispatcher.locale;
      _testResults['系统语言检测'] = {
        'result': locale.countryCode == 'CN',
        'details': '语言: ${locale.languageCode}, 地区: ${locale.countryCode}',
        'reliability': '70%',
        'speed': '极快',
      };
    } catch (e) {
      _testResults['系统语言检测'] = {
        'result': false,
        'details': '检测失败: $e',
        'reliability': '0%',
        'speed': '极快',
      };
    }
    setState(() {});
  }

  Future<void> _testTimeZone() async {
    try {
      final timeZone = DateTime.now().timeZoneName;
      final offset = DateTime.now().timeZoneOffset;
      final isChina = timeZone.contains('China') || 
                     timeZone.contains('Asia/Shanghai') ||
                     timeZone.contains('Asia/Beijing') ||
                     offset.inHours == 8;
      
      _testResults['时区检测'] = {
        'result': isChina,
        'details': '时区: $timeZone, 偏移: ${offset.inHours}小时',
        'reliability': '60%',
        'speed': '极快',
      };
    } catch (e) {
      _testResults['时区检测'] = {
        'result': false,
        'details': '检测失败: $e',
        'reliability': '0%',
        'speed': '极快',
      };
    }
    setState(() {});
  }

  Future<void> _testIPDetection() async {
    try {
      // 测试多个IP检测API
      final results = <String, dynamic>{};
      
      // API 1: ipapi.co
      try {
        final response1 = await http.get(
          Uri.parse('https://ipapi.co/json/'),
        ).timeout(const Duration(seconds: 5));
        
        if (response1.statusCode == 200) {
          final data1 = json.decode(response1.body);
          results['ipapi.co'] = {
            'country': data1['country_code'],
            'city': data1['city'],
            'ip': data1['ip'],
          };
        }
      } catch (e) {
        results['ipapi.co'] = '失败: $e';
      }
      
      // API 2: ip-api.com
      try {
        final response2 = await http.get(
          Uri.parse('http://ip-api.com/json/'),
        ).timeout(const Duration(seconds: 5));
        
        if (response2.statusCode == 200) {
          final data2 = json.decode(response2.body);
          results['ip-api.com'] = {
            'country': data2['countryCode'],
            'city': data2['city'],
            'ip': data2['query'],
          };
        }
      } catch (e) {
        results['ip-api.com'] = '失败: $e';
      }

      // 判断结果
      bool isChina = false;
      if (results['ipapi.co'] is Map && results['ipapi.co']['country'] == 'CN') {
        isChina = true;
      } else if (results['ip-api.com'] is Map && results['ip-api.com']['country'] == 'CN') {
        isChina = true;
      }

      _testResults['IP地址检测'] = {
        'result': isChina,
        'details': results.toString(),
        'reliability': '95%',
        'speed': '中等',
      };
    } catch (e) {
      _testResults['IP地址检测'] = {
        'result': false,
        'details': '检测失败: $e',
        'reliability': '0%',
        'speed': '中等',
      };
    }
    setState(() {});
  }

  Future<void> _testConnectivity() async {
    try {
      final results = <String, bool>{};
      
      // 测试百度
      try {
        final baiduResponse = await http.head(
          Uri.parse('https://www.baidu.com'),
        ).timeout(const Duration(seconds: 3));
        results['百度'] = baiduResponse.statusCode == 200;
      } catch (e) {
        results['百度'] = false;
      }
      
      // 测试Google
      try {
        final googleResponse = await http.head(
          Uri.parse('https://www.google.com'),
        ).timeout(const Duration(seconds: 3));
        results['Google'] = googleResponse.statusCode == 200;
      } catch (e) {
        results['Google'] = false;
      }

      // 判断逻辑：能访问百度但不能访问Google，可能在中国
      final isChina = results['百度'] == true && results['Google'] == false;

      _testResults['网络连通性检测'] = {
        'result': isChina,
        'details': '百度: ${results['百度']}, Google: ${results['Google']}',
        'reliability': '85%',
        'speed': '慢',
      };
    } catch (e) {
      _testResults['网络连通性检测'] = {
        'result': false,
        'details': '检测失败: $e',
        'reliability': '0%',
        'speed': '慢',
      };
    }
    setState(() {});
  }

  Future<void> _testComprehensive() async {
    try {
      final regionService = RegionService.instance;
      regionService.resetCache(); // 重置缓存以获取最新结果
      
      final result = await regionService.isInChina();
      
      _testResults['综合检测'] = {
        'result': result,
        'details': '使用多重检测算法，权重评分',
        'reliability': '98%',
        'speed': '中等',
      };
    } catch (e) {
      _testResults['综合检测'] = {
        'result': false,
        'details': '检测失败: $e',
        'reliability': '0%',
        'speed': '中等',
      };
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('地区检测测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _runAllTests,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _testResults.length,
              itemBuilder: (context, index) {
                final entry = _testResults.entries.elementAt(index);
                final testName = entry.key;
                final testData = entry.value;
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              testData['result'] ? Icons.location_on : Icons.location_off,
                              color: testData['result'] ? Colors.red : Colors.grey,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              testName,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: testData['result'] ? Colors.red.shade100 : Colors.grey.shade100,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                testData['result'] ? '中国' : '海外',
                                style: TextStyle(
                                  color: testData['result'] ? Colors.red : Colors.grey,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '详情: ${testData['details']}',
                          style: const TextStyle(fontSize: 14),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text('可靠性: ${testData['reliability']}'),
                            const SizedBox(width: 16),
                            Text('速度: ${testData['speed']}'),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }
}
