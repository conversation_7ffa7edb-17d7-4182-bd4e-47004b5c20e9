import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../generated/app_localizations.dart';
import '../../models/device_info.dart';
import '../../services/device_manager.dart';
import '../../services/data_simulator.dart';

class DeviceListPage extends StatefulWidget {
  const DeviceListPage({super.key});

  @override
  State<DeviceListPage> createState() => _DeviceListPageState();
}

class _DeviceListPageState extends State<DeviceListPage> {
  late DeviceManager _deviceManager;
  late DataSimulator _dataSimulator;
  bool _isScanning = false;
  bool _useSimulator = true; // 默认使用模拟器

  @override
  void initState() {
    super.initState();
    _deviceManager = DeviceManager();
    _dataSimulator = DataSimulator();
    _initializeDeviceManager();
  }

  Future<void> _initializeDeviceManager() async {
    await _deviceManager.initialize();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.deviceList),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        actions: [
          // 模拟器切换开关
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'toggle_simulator') {
                setState(() {
                  _useSimulator = !_useSimulator;
                });
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'toggle_simulator',
                child: Row(
                  children: [
                    Icon(_useSimulator ? Icons.toggle_on : Icons.toggle_off),
                    const SizedBox(width: 8),
                    Text(_useSimulator ? '关闭模拟器' : '开启模拟器'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: ChangeNotifierProvider.value(
        value: _deviceManager,
        child: Consumer<DeviceManager>(
          builder: (context, deviceManager, child) {
            return Column(
              children: [
                // 连接状态栏
                _buildConnectionStatusBar(context, deviceManager, l10n),
                
                // 扫描控制栏
                _buildScanControlBar(context, deviceManager, l10n),
                
                // 设备列表
                Expanded(
                  child: _buildDeviceList(context, deviceManager, l10n),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildConnectionStatusBar(BuildContext context, DeviceManager deviceManager, AppLocalizations l10n) {
    final connectedDevice = deviceManager.connectedDevice;
    final connectionState = deviceManager.connectionState;
    
    if (connectedDevice == null) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        color: Colors.grey.shade100,
        child: Row(
          children: [
            const Icon(Icons.bluetooth_disabled, color: Colors.grey),
            const SizedBox(width: 8),
            Text(
              l10n.disconnected,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (connectionState) {
      case DeviceConnectionState.connected:
        statusColor = Colors.green;
        statusIcon = Icons.bluetooth_connected;
        statusText = l10n.connected;
        break;
      case DeviceConnectionState.connecting:
        statusColor = Colors.orange;
        statusIcon = Icons.bluetooth_searching;
        statusText = l10n.connecting;
        break;
      case DeviceConnectionState.disconnecting:
        statusColor = Colors.orange;
        statusIcon = Icons.bluetooth_disabled;
        statusText = l10n.disconnecting;
        break;
      case DeviceConnectionState.error:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = l10n.connectionError;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.bluetooth_disabled;
        statusText = l10n.disconnected;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      color: statusColor.withOpacity(0.1),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  statusText,
                  style: TextStyle(
                    fontSize: 16,
                    color: statusColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  connectedDevice.name,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          if (connectionState == DeviceConnectionState.connected)
            TextButton(
              onPressed: () => _deviceManager.disconnectDevice(),
              child: Text(l10n.disconnectDevice),
            ),
        ],
      ),
    );
  }

  Widget _buildScanControlBar(BuildContext context, DeviceManager deviceManager, AppLocalizations l10n) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _isScanning ? _stopScan : _startScan,
              icon: Icon(_isScanning ? Icons.stop : Icons.search),
              label: Text(_isScanning ? l10n.stopScan : l10n.scanDevices),
              style: ElevatedButton.styleFrom(
                backgroundColor: _isScanning ? Colors.red : Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          if (_useSimulator) ...[
            const SizedBox(width: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange.shade100,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                '模拟器',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.orange,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDeviceList(BuildContext context, DeviceManager deviceManager, AppLocalizations l10n) {
    List<DeviceInfo> devices = _useSimulator 
        ? _dataSimulator.getMockDevices() 
        : deviceManager.availableDevices;

    if (devices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bluetooth_searching,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              l10n.noDevicesFound,
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _useSimulator ? '模拟器模式' : '请开始扫描设备',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: devices.length,
      itemBuilder: (context, index) {
        final device = devices[index];
        return _buildDeviceCard(context, device, deviceManager, l10n);
      },
    );
  }

  Widget _buildDeviceCard(BuildContext context, DeviceInfo device, DeviceManager deviceManager, AppLocalizations l10n) {
    final isConnected = deviceManager.connectedDevice?.macAddress == device.macAddress;
    final isConnecting = deviceManager.connectionState == DeviceConnectionState.connecting;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isConnected ? Colors.green.shade100 : Colors.blue.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            isConnected ? Icons.bluetooth_connected : Icons.bluetooth,
            color: isConnected ? Colors.green.shade700 : Colors.blue.shade700,
          ),
        ),
        title: Text(
          device.name,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'MAC: ${device.macAddress}',
              style: const TextStyle(fontSize: 12),
            ),
            const SizedBox(height: 2),
            Row(
              children: [
                Text(
                  device.generation == DeviceGeneration.generation1 
                      ? l10n.generation1 
                      : l10n.generation2,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '${l10n.signalStrength}: ${_getSignalStrengthText(device.rssi, l10n)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: isConnected
            ? Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  l10n.connected,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              )
            : ElevatedButton(
                onPressed: isConnecting ? null : () => _connectDevice(device),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: Text(
                  isConnecting ? l10n.connecting : l10n.connectDevice,
                  style: const TextStyle(fontSize: 12),
                ),
              ),
      ),
    );
  }

  String _getSignalStrengthText(int rssi, AppLocalizations l10n) {
    if (rssi >= -50) return l10n.excellent;
    if (rssi >= -60) return l10n.good;
    if (rssi >= -70) return l10n.fair;
    return l10n.poor;
  }

  Future<void> _startScan() async {
    setState(() {
      _isScanning = true;
    });

    if (_useSimulator) {
      // 模拟扫描延迟
      await Future.delayed(const Duration(seconds: 2));
    } else {
      await _deviceManager.startDeviceScan();
    }

    // 自动停止扫描
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        _stopScan();
      }
    });
  }

  Future<void> _stopScan() async {
    setState(() {
      _isScanning = false;
    });

    if (!_useSimulator) {
      await _deviceManager.stopDeviceScan();
    }
  }

  Future<void> _connectDevice(DeviceInfo device) async {
    if (_useSimulator) {
      // 模拟连接
      bool success = await _dataSimulator.simulateConnection();
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('模拟连接成功: ${device.name}')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('模拟连接失败: ${device.name}')),
        );
      }
    } else {
      bool success = await _deviceManager.connectDevice(device);
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('连接成功: ${device.name}')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('连接失败: ${device.name}')),
        );
      }
    }
  }

  @override
  void dispose() {
    _stopScan();
    super.dispose();
  }
} 