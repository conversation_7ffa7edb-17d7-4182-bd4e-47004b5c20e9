import 'package:flutter/material.dart';
import 'package:xtand_app/generated/app_localizations.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      body: Safe<PERSON><PERSON>(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // 用户信息区域
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    const CircleAvatar(
                      radius: 40,
                      backgroundColor: Colors.blue,
                      child: Icon(
                        Icons.person,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'User Name',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '<EMAIL>',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // 功能选项列表
              Expanded(
                child: ListView(
                  children: [
                    _buildMenuTile(
                      context,
                      icon: Icons.bluetooth,
                      title: l10n.device,
                      subtitle: l10n.deviceConnection,
                      onTap: () {
                        Navigator.pushNamed(context, '/device-list');
                      },
                    ),
                    _buildMenuTile(
                      context,
                      icon: Icons.settings,
                      title: '设置',
                      subtitle: '应用设置和偏好',
                      onTap: () {
                        // TODO: 导航到设置页面
                      },
                    ),
                    _buildMenuTile(
                      context,
                      icon: Icons.help_outline,
                      title: '帮助',
                      subtitle: '使用帮助和常见问题',
                      onTap: () {
                        // TODO: 导航到帮助页面
                      },
                    ),
                    _buildMenuTile(
                      context,
                      icon: Icons.info_outline,
                      title: '关于',
                      subtitle: '应用版本和信息',
                      onTap: () {
                        // TODO: 显示关于对话框
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Colors.blue.shade700,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey,
        ),
        onTap: onTap,
      ),
    );
  }
} 