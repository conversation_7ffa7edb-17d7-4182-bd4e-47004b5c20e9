/// 设备信息模型
class DeviceInfo {
  final String name;
  final String macAddress;
  final String manufacturerId;
  final DeviceGeneration generation;
  final bool isConnected;
  final int rssi;

  const DeviceInfo({
    required this.name,
    required this.macAddress,
    required this.manufacturerId,
    required this.generation,
    this.isConnected = false,
    this.rssi = 0,
  });

  DeviceInfo copyWith({
    String? name,
    String? macAddress,
    String? manufacturerId,
    DeviceGeneration? generation,
    bool? isConnected,
    int? rssi,
  }) {
    return DeviceInfo(
      name: name ?? this.name,
      macAddress: macAddress ?? this.macAddress,
      manufacturerId: manufacturerId ?? this.manufacturerId,
      generation: generation ?? this.generation,
      isConnected: isConnected ?? this.isConnected,
      rssi: rssi ?? this.rssi,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'macAddress': macAddress,
      'manufacturerId': manufacturerId,
      'generation': generation.index,
      'isConnected': isConnected,
      'rssi': rssi,
    };
  }

  factory DeviceInfo.fromJson(Map<String, dynamic> json) {
    return DeviceInfo(
      name: json['name'] as String,
      macAddress: json['macAddress'] as String,
      manufacturerId: json['manufacturerId'] as String,
      generation: DeviceGeneration.values[json['generation'] as int],
      isConnected: json['isConnected'] as bool? ?? false,
      rssi: json['rssi'] as int? ?? 0,
    );
  }
}

/// 设备代数枚举
enum DeviceGeneration {
  generation1, // 第一代：实时传输
  generation2, // 第二代：数据存储后传输
}

/// 设备连接状态
enum DeviceConnectionState {
  disconnected,
  connecting,
  connected,
  disconnecting,
  error,
}

/// 蓝牙服务UUID常量
class BluetoothUUIDs {
  static const String deviceName = 'XTAND-AirH-G1';
  static const String manufacturerId = '0x04F7';
  static const String macAddress = 'DE:40:09:35:AE:F8';
  
  // 核心服务UUID
  static const String uartServiceUuid = 'a6ed0201-d344-460a-8075-b9e8ec90d71b';
  static const String txCharacteristicUuid = 'a6ed0202-d344-460a-8075-b9e8ec90d71b';
  static const String rxCharacteristicUuid = 'a6ed0203-d344-460a-8075-b9e8ec90d71b';
  static const String flowControlUuid = 'a6ed0204-d344-460a-8075-b9e8ec90d71b';
  
  // OTA服务UUID（暂不实现）
  static const String otaServiceUuid = 'a6ed0401-d344-460a-8075-b9e8ec90d71b';
  
  // 客户端特征配置描述符
  static const String cccdUuid = '00002902-0000-1000-8000-00805f9b34fb';
} 