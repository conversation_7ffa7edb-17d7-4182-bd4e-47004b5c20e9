import 'dart:typed_data';

/// 设备状态数据模型
class DeviceStatus {
  final int rgbColor;
  final int rgbMode;
  final double pressureValue; // 气压值 (kPa)
  final double batteryVoltage; // 电池电压
  final DateTime timestamp;

  const DeviceStatus({
    required this.rgbColor,
    required this.rgbMode,
    required this.pressureValue,
    required this.batteryVoltage,
    required this.timestamp,
  });

  DeviceStatus copyWith({
    int? rgbColor,
    int? rgbMode,
    double? pressureValue,
    double? batteryVoltage,
    DateTime? timestamp,
  }) {
    return DeviceStatus(
      rgbColor: rgbColor ?? this.rgbColor,
      rgbMode: rgbMode ?? this.rgbMode,
      pressureValue: pressureValue ?? this.pressureValue,
      batteryVoltage: batteryVoltage ?? this.batteryVoltage,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'rgbColor': rgbColor,
      'rgbMode': rgbMode,
      'pressureValue': pressureValue,
      'batteryVoltage': batteryVoltage,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory DeviceStatus.fromJson(Map<String, dynamic> json) {
    return DeviceStatus(
      rgbColor: json['rgbColor'] as int,
      rgbMode: json['rgbMode'] as int,
      pressureValue: json['pressureValue'] as double,
      batteryVoltage: json['batteryVoltage'] as double,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

/// 运动状态枚举
enum ExerciseState {
  standing,  // 站立
  walking,   // 行走
  running,   // 跑步
  jumping,   // 跳跃
}

/// 实时运动数据（第一代设备）
class RealTimeExerciseData {
  final ExerciseState state;
  final double impactForce;        // 冲击力
  final double dampingForce;       // 减缓冲击力
  final int impactCount;           // 冲击次数
  final int jumpCount;             // 跳跃次数
  final double jumpHeight;         // 跳跃高度
  final GPSData? gpsData;          // GPS位置（可选）
  final DateTime timestamp;

  const RealTimeExerciseData({
    required this.state,
    required this.impactForce,
    required this.dampingForce,
    required this.impactCount,
    required this.jumpCount,
    required this.jumpHeight,
    this.gpsData,
    required this.timestamp,
  });

  RealTimeExerciseData copyWith({
    ExerciseState? state,
    double? impactForce,
    double? dampingForce,
    int? impactCount,
    int? jumpCount,
    double? jumpHeight,
    GPSData? gpsData,
    DateTime? timestamp,
  }) {
    return RealTimeExerciseData(
      state: state ?? this.state,
      impactForce: impactForce ?? this.impactForce,
      dampingForce: dampingForce ?? this.dampingForce,
      impactCount: impactCount ?? this.impactCount,
      jumpCount: jumpCount ?? this.jumpCount,
      jumpHeight: jumpHeight ?? this.jumpHeight,
      gpsData: gpsData ?? this.gpsData,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state.index,
      'impactForce': impactForce,
      'dampingForce': dampingForce,
      'impactCount': impactCount,
      'jumpCount': jumpCount,
      'jumpHeight': jumpHeight,
      'gpsData': gpsData?.toJson(),
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory RealTimeExerciseData.fromJson(Map<String, dynamic> json) {
    return RealTimeExerciseData(
      state: ExerciseState.values[json['state'] as int],
      impactForce: json['impactForce'] as double,
      dampingForce: json['dampingForce'] as double,
      impactCount: json['impactCount'] as int,
      jumpCount: json['jumpCount'] as int,
      jumpHeight: json['jumpHeight'] as double,
      gpsData: json['gpsData'] != null ? GPSData.fromJson(json['gpsData']) : null,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

/// 完整运动数据包（运动结束后同步）
class CompleteExerciseData {
  final DateTime startTime;        // 运动开始时间
  final DateTime endTime;          // 运动结束时间
  final int totalSteps;            // 累计步数
  final double calories;           // 总卡路里消耗
  final double avgSpeed;           // 平均速度
  final List<GPSData> gpsTrack;    // GPS轨迹
  final List<RealTimeExerciseData> realtimeData; // 实时数据记录

  const CompleteExerciseData({
    required this.startTime,
    required this.endTime,
    required this.totalSteps,
    required this.calories,
    required this.avgSpeed,
    required this.gpsTrack,
    required this.realtimeData,
  });

  Duration get duration => endTime.difference(startTime);

  CompleteExerciseData copyWith({
    DateTime? startTime,
    DateTime? endTime,
    int? totalSteps,
    double? calories,
    double? avgSpeed,
    List<GPSData>? gpsTrack,
    List<RealTimeExerciseData>? realtimeData,
  }) {
    return CompleteExerciseData(
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      totalSteps: totalSteps ?? this.totalSteps,
      calories: calories ?? this.calories,
      avgSpeed: avgSpeed ?? this.avgSpeed,
      gpsTrack: gpsTrack ?? this.gpsTrack,
      realtimeData: realtimeData ?? this.realtimeData,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'totalSteps': totalSteps,
      'calories': calories,
      'avgSpeed': avgSpeed,
      'gpsTrack': gpsTrack.map((e) => e.toJson()).toList(),
      'realtimeData': realtimeData.map((e) => e.toJson()).toList(),
    };
  }

  factory CompleteExerciseData.fromJson(Map<String, dynamic> json) {
    return CompleteExerciseData(
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      totalSteps: json['totalSteps'] as int,
      calories: json['calories'] as double,
      avgSpeed: json['avgSpeed'] as double,
      gpsTrack: (json['gpsTrack'] as List).map((e) => GPSData.fromJson(e)).toList(),
      realtimeData: (json['realtimeData'] as List).map((e) => RealTimeExerciseData.fromJson(e)).toList(),
    );
  }
}

/// GPS数据模型
class GPSData {
  final double latitude;   // 纬度
  final double longitude;  // 经度
  final double altitude;   // 海拔
  final DateTime timestamp;

  const GPSData({
    required this.latitude,
    required this.longitude,
    required this.altitude,
    required this.timestamp,
  });

  GPSData copyWith({
    double? latitude,
    double? longitude,
    double? altitude,
    DateTime? timestamp,
  }) {
    return GPSData(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      altitude: altitude ?? this.altitude,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'altitude': altitude,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory GPSData.fromJson(Map<String, dynamic> json) {
    return GPSData(
      latitude: json['latitude'] as double,
      longitude: json['longitude'] as double,
      altitude: json['altitude'] as double,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

/// 数据帧类型枚举
enum DataFrameType {
  deviceStatus(0x00),    // 设备状态帧
  exerciseData(0x01),    // 运动数据帧（待定）
  unknown(0xFF);         // 未知帧类型

  const DataFrameType(this.id);
  final int id;

  static DataFrameType fromId(int id) {
    return DataFrameType.values.firstWhere(
      (type) => type.id == id,
      orElse: () => DataFrameType.unknown,
    );
  }
} 