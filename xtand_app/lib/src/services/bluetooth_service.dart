import 'dart:async';
import 'dart:typed_data';
import 'package:flutter_blue_plus/flutter_blue_plus.dart' as fbp;
import '../models/device_info.dart';
import '../models/exercise_data.dart';
import 'protocol_parser.dart';

/// 蓝牙管理服务类
class BluetoothManager {
  static final BluetoothManager _instance = BluetoothManager._internal();
  factory BluetoothManager() => _instance;
  BluetoothManager._internal();

  // 状态流控制器
  final StreamController<List<DeviceInfo>> _devicesController = StreamController<List<DeviceInfo>>.broadcast();
  final StreamController<DeviceConnectionState> _connectionStateController = StreamController<DeviceConnectionState>.broadcast();
  final StreamController<DeviceStatus> _deviceStatusController = StreamController<DeviceStatus>.broadcast();
  final StreamController<RealTimeExerciseData> _exerciseDataController = StreamController<RealTimeExerciseData>.broadcast();

  // 当前连接状态
  fbp.BluetoothDevice? _connectedDevice;
  fbp.BluetoothCharacteristic? _txCharacteristic;
  fbp.BluetoothCharacteristic? _rxCharacteristic;
  DeviceConnectionState _connectionState = DeviceConnectionState.disconnected;
  
  // 扫描到的设备列表
  final List<DeviceInfo> _discoveredDevices = [];
  
  // 数据接收缓冲区
  final List<int> _dataBuffer = [];

  // 公共流
  Stream<List<DeviceInfo>> get devicesStream => _devicesController.stream;
  Stream<DeviceConnectionState> get connectionStateStream => _connectionStateController.stream;
  Stream<DeviceStatus> get deviceStatusStream => _deviceStatusController.stream;
  Stream<RealTimeExerciseData> get exerciseDataStream => _exerciseDataController.stream;

  // 获取当前状态
  DeviceConnectionState get connectionState => _connectionState;
  DeviceInfo? get connectedDevice => _connectedDevice != null ? _getDeviceInfo(_connectedDevice!) : null;
  List<DeviceInfo> get discoveredDevices => List.unmodifiable(_discoveredDevices);

  /// 初始化蓝牙服务
  Future<bool> initialize() async {
    try {
      // 检查蓝牙是否可用
      if (!await fbp.FlutterBluePlus.isAvailable) {
        throw Exception('蓝牙不可用');
      }

      // 检查蓝牙是否开启
      if (await fbp.FlutterBluePlus.adapterState.first != fbp.BluetoothAdapterState.on) {
        throw Exception('蓝牙未开启');
      }

      return true;
    } catch (e) {
      print('蓝牙初始化失败: $e');
      return false;
    }
  }

  /// 开始扫描设备
  Future<void> startScan({Duration timeout = const Duration(seconds: 10)}) async {
    try {
      _discoveredDevices.clear();
      _devicesController.add(_discoveredDevices);

      // 开始扫描
      await fbp.FlutterBluePlus.startScan(timeout: timeout);

      // 监听扫描结果
      fbp.FlutterBluePlus.scanResults.listen((results) {
        for (fbp.ScanResult result in results) {
          _processScanResult(result);
        }
      });

    } catch (e) {
      print('扫描失败: $e');
    }
  }

  /// 停止扫描
  Future<void> stopScan() async {
    await fbp.FlutterBluePlus.stopScan();
  }

  /// 连接设备
  Future<bool> connectDevice(DeviceInfo deviceInfo) async {
    try {
      _updateConnectionState(DeviceConnectionState.connecting);

      // 查找对应的蓝牙设备
      fbp.BluetoothDevice? device = await _findBluetoothDevice(deviceInfo.macAddress);
      if (device == null) {
        throw Exception('未找到设备');
      }

      // 连接设备
      await device.connect(timeout: const Duration(seconds: 10));
      _connectedDevice = device;

      // 发现服务
      List<fbp.BluetoothService> services = await device.discoverServices();
      
      // 查找UART服务
      fbp.BluetoothService? uartService = services.firstWhere(
        (service) => service.uuid.toString().toLowerCase() == BluetoothUUIDs.uartServiceUuid.toLowerCase(),
        orElse: () => throw Exception('未找到UART服务'),
      );

      // 获取特征
      _txCharacteristic = uartService.characteristics.firstWhere(
        (char) => char.uuid.toString().toLowerCase() == BluetoothUUIDs.txCharacteristicUuid.toLowerCase(),
        orElse: () => throw Exception('未找到TX特征'),
      );

      _rxCharacteristic = uartService.characteristics.firstWhere(
        (char) => char.uuid.toString().toLowerCase() == BluetoothUUIDs.rxCharacteristicUuid.toLowerCase(),
        orElse: () => throw Exception('未找到RX特征'),
      );

      // 启用TX特征的通知
      await _txCharacteristic!.setNotifyValue(true);
      
      // 监听数据接收
      _txCharacteristic!.value.listen(_onDataReceived);

      _updateConnectionState(DeviceConnectionState.connected);
      return true;

    } catch (e) {
      print('连接失败: $e');
      _updateConnectionState(DeviceConnectionState.error);
      return false;
    }
  }

  /// 断开连接
  Future<void> disconnect() async {
    try {
      _updateConnectionState(DeviceConnectionState.disconnecting);
      
      if (_connectedDevice != null) {
        await _connectedDevice!.disconnect();
        _connectedDevice = null;
        _txCharacteristic = null;
        _rxCharacteristic = null;
      }
      
      _updateConnectionState(DeviceConnectionState.disconnected);
    } catch (e) {
      print('断开连接失败: $e');
      _updateConnectionState(DeviceConnectionState.error);
    }
  }

  /// 发送数据
  Future<bool> sendData(Uint8List data) async {
    try {
      if (_rxCharacteristic == null) {
        throw Exception('设备未连接');
      }

      await _rxCharacteristic!.write(data, withoutResponse: true);
      return true;
    } catch (e) {
      print('发送数据失败: $e');
      return false;
    }
  }

  /// 查询设备状态
  Future<bool> queryDeviceStatus() async {
    Uint8List frame = FrameBuilder.buildStatusQueryFrame();
    return await sendData(frame);
  }

  /// 控制设备
  Future<bool> controlDevice(int rgbColor, int rgbMode) async {
    Uint8List frame = FrameBuilder.buildControlFrame(rgbColor, rgbMode);
    return await sendData(frame);
  }

  /// 处理扫描结果
  void _processScanResult(fbp.ScanResult result) {
    // 检查设备名称
    if (result.device.name != BluetoothUUIDs.deviceName) {
      return;
    }

    // 验证广播包
    if (!ProtocolParser.isValidDevice(result.advertisementData.manufacturerData.values.first)) {
      return;
    }

    // 检查是否已存在
    bool exists = _discoveredDevices.any((device) => device.macAddress == result.device.id.id);
    if (exists) {
      return;
    }

    // 添加设备
    DeviceInfo deviceInfo = DeviceInfo(
      name: result.device.name,
      macAddress: result.device.id.id,
      manufacturerId: BluetoothUUIDs.manufacturerId,
      generation: DeviceGeneration.generation1, // 默认第一代，后续可根据具体信息判断
      rssi: result.rssi,
    );

    _discoveredDevices.add(deviceInfo);
    _devicesController.add(_discoveredDevices);
  }

  /// 数据接收处理
  void _onDataReceived(List<int> data) {
    _dataBuffer.addAll(data);
    _processReceivedData();
  }

  /// 处理接收到的数据
  void _processReceivedData() {
    while (_dataBuffer.isNotEmpty) {
      // 查找帧头
      int headerIndex = _dataBuffer.indexOf(ProtocolParser.frameHeader);
      if (headerIndex == -1) {
        _dataBuffer.clear();
        break;
      }

      // 移除帧头前的无效数据
      if (headerIndex > 0) {
        _dataBuffer.removeRange(0, headerIndex);
      }

      // 检查是否有足够的数据解析帧
      if (_dataBuffer.length < ProtocolParser.minFrameLength) {
        break;
      }

      // 获取数据长度
      if (_dataBuffer.length < 3) {
        break;
      }
      
      int dataLength = _dataBuffer[2];
      int frameLength = 3 + dataLength + 2 + 1; // 帧头+帧ID+长度+数据+校验和+帧尾

      if (_dataBuffer.length < frameLength) {
        break;
      }

      // 提取完整帧
      Uint8List frame = Uint8List.fromList(_dataBuffer.sublist(0, frameLength));
      _dataBuffer.removeRange(0, frameLength);

      // 解析帧
      ParseResult result = ProtocolParser.parseFrame(frame);
      if (result.success) {
        _handleParsedData(result.frameType!, result.data);
      } else {
        print('数据解析失败: ${result.error}');
      }
    }
  }

  /// 处理解析后的数据
  void _handleParsedData(DataFrameType frameType, dynamic data) {
    switch (frameType) {
      case DataFrameType.deviceStatus:
        if (data is DeviceStatus) {
          _deviceStatusController.add(data);
        }
        break;
      case DataFrameType.exerciseData:
        if (data is RealTimeExerciseData) {
          _exerciseDataController.add(data);
        }
        break;
      default:
        print('未知数据类型: $frameType');
    }
  }

  /// 查找蓝牙设备
  Future<fbp.BluetoothDevice?> _findBluetoothDevice(String macAddress) async {
    List<fbp.BluetoothDevice> devices = fbp.FlutterBluePlus.connectedDevices;
    
    for (fbp.BluetoothDevice device in devices) {
      if (device.id.id == macAddress) {
        return device;
      }
    }

    // 如果没有找到已连接的设备，尝试从扫描结果中查找
    await startScan(timeout: const Duration(seconds: 5));
    
    return null;
  }

  /// 获取设备信息
  DeviceInfo _getDeviceInfo(fbp.BluetoothDevice device) {
    return DeviceInfo(
      name: device.name,
      macAddress: device.id.id,
      manufacturerId: BluetoothUUIDs.manufacturerId,
      generation: DeviceGeneration.generation1,
      isConnected: device.isConnected,
    );
  }

  /// 更新连接状态
  void _updateConnectionState(DeviceConnectionState state) {
    _connectionState = state;
    _connectionStateController.add(state);
  }

  /// 释放资源
  void dispose() {
    _devicesController.close();
    _connectionStateController.close();
    _deviceStatusController.close();
    _exerciseDataController.close();
  }
} 