import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import '../models/device_info.dart';
import '../models/exercise_data.dart';
import 'protocol_parser.dart';

/// 数据模拟器 - 用于在没有实际设备时进行测试
class DataSimulator {
  static final DataSimulator _instance = DataSimulator._internal();
  factory DataSimulator() => _instance;
  DataSimulator._internal();

  final Random _random = Random();
  Timer? _simulationTimer;
  bool _isSimulating = false;

  // 模拟设备信息
  static const List<DeviceInfo> _mockDevices = [
    DeviceInfo(
      name: 'XTAND-AirH-G1',
      macAddress: 'DE:40:09:35:AE:F8',
      manufacturerId: '0x04F7',
      generation: DeviceGeneration.generation1,
      rssi: -45,
    ),
    DeviceInfo(
      name: 'XTAND-AirH-G2',
      macAddress: 'DE:40:09:35:AE:F9',
      manufacturerId: '0x04F7',
      generation: DeviceGeneration.generation2,
      rssi: -52,
    ),
  ];

  // 模拟数据基础值
  double _baseTemperature = 25.0;
  double _basePressure = 101.3;
  double _batteryVoltage = 4.0;
  int _jumpCount = 0;
  int _impactCount = 0;
  ExerciseState _currentState = ExerciseState.standing;

  /// 获取模拟设备列表
  List<DeviceInfo> getMockDevices() {
    return _mockDevices.map((device) => device.copyWith(
      rssi: -40 + _random.nextInt(40), // 随机信号强度
    )).toList();
  }

  /// 生成模拟设备状态数据
  DeviceStatus generateMockDeviceStatus() {
    // 模拟电池电压缓慢下降
    _batteryVoltage = (_batteryVoltage - 0.001).clamp(3.0, 4.2);
    
    // 模拟压力值波动
    double pressureVariation = (_random.nextDouble() - 0.5) * 2.0;
    double currentPressure = (_basePressure + pressureVariation).clamp(95.0, 105.0);

    return DeviceStatus(
      rgbColor: _random.nextInt(256),
      rgbMode: _random.nextInt(4),
      pressureValue: currentPressure,
      batteryVoltage: _batteryVoltage,
      timestamp: DateTime.now(),
    );
  }

  /// 生成模拟运动数据
  RealTimeExerciseData generateMockExerciseData() {
    // 随机改变运动状态
    if (_random.nextDouble() < 0.1) {
      _currentState = ExerciseState.values[_random.nextInt(ExerciseState.values.length)];
    }

    // 根据运动状态生成相应数据
    double impactForce = 0.0;
    double dampingForce = 0.0;
    double jumpHeight = 0.0;

    switch (_currentState) {
      case ExerciseState.standing:
        impactForce = 0.5 + _random.nextDouble() * 0.5;
        dampingForce = 0.2 + _random.nextDouble() * 0.3;
        break;
      case ExerciseState.walking:
        impactForce = 1.0 + _random.nextDouble() * 1.0;
        dampingForce = 0.5 + _random.nextDouble() * 0.5;
        if (_random.nextDouble() < 0.3) _impactCount++;
        break;
      case ExerciseState.running:
        impactForce = 2.0 + _random.nextDouble() * 2.0;
        dampingForce = 1.0 + _random.nextDouble() * 1.0;
        if (_random.nextDouble() < 0.7) _impactCount++;
        break;
      case ExerciseState.jumping:
        impactForce = 3.0 + _random.nextDouble() * 3.0;
        dampingForce = 1.5 + _random.nextDouble() * 1.5;
        jumpHeight = 0.2 + _random.nextDouble() * 0.8;
        if (_random.nextDouble() < 0.9) {
          _jumpCount++;
          _impactCount++;
        }
        break;
    }

    return RealTimeExerciseData(
      state: _currentState,
      impactForce: impactForce,
      dampingForce: dampingForce,
      impactCount: _impactCount,
      jumpCount: _jumpCount,
      jumpHeight: jumpHeight,
      gpsData: _generateMockGPSData(),
      timestamp: DateTime.now(),
    );
  }

  /// 生成模拟GPS数据
  GPSData _generateMockGPSData() {
    // 模拟在北京附近的GPS坐标
    double baseLat = 39.9042;
    double baseLng = 116.4074;
    
    double latVariation = (_random.nextDouble() - 0.5) * 0.01;
    double lngVariation = (_random.nextDouble() - 0.5) * 0.01;
    
    return GPSData(
      latitude: baseLat + latVariation,
      longitude: baseLng + lngVariation,
      altitude: 50.0 + _random.nextDouble() * 100.0,
      timestamp: DateTime.now(),
    );
  }

  /// 生成模拟完整运动数据
  CompleteExerciseData generateMockCompleteExerciseData({
    Duration? duration,
    int? stepCount,
  }) {
    DateTime now = DateTime.now();
    Duration exerciseDuration = duration ?? Duration(minutes: 30 + _random.nextInt(60));
    DateTime startTime = now.subtract(exerciseDuration);
    
    List<RealTimeExerciseData> realtimeData = [];
    List<GPSData> gpsTrack = [];
    
    // 生成运动期间的数据点
    int dataPoints = exerciseDuration.inMinutes;
    for (int i = 0; i < dataPoints; i++) {
      DateTime timestamp = startTime.add(Duration(minutes: i));
      
      RealTimeExerciseData data = RealTimeExerciseData(
        state: ExerciseState.values[_random.nextInt(ExerciseState.values.length)],
        impactForce: _random.nextDouble() * 4.0,
        dampingForce: _random.nextDouble() * 2.0,
        impactCount: _random.nextInt(100),
        jumpCount: _random.nextInt(50),
        jumpHeight: _random.nextDouble() * 1.0,
        timestamp: timestamp,
      );
      
      realtimeData.add(data);
      gpsTrack.add(_generateMockGPSData());
    }

    return CompleteExerciseData(
      startTime: startTime,
      endTime: now,
      totalSteps: stepCount ?? (2000 + _random.nextInt(8000)),
      calories: 200.0 + _random.nextDouble() * 500.0,
      avgSpeed: 3.0 + _random.nextDouble() * 7.0,
      gpsTrack: gpsTrack,
      realtimeData: realtimeData,
    );
  }

  /// 生成模拟数据帧
  Uint8List generateMockDataFrame(DataFrameType frameType) {
    Uint8List data;
    
    switch (frameType) {
      case DataFrameType.deviceStatus:
        data = _generateDeviceStatusFrame();
        break;
      case DataFrameType.exerciseData:
        data = _generateExerciseDataFrame();
        break;
      default:
        data = Uint8List.fromList([0x00, 0x01, 0x02, 0x03]);
    }
    
    return ProtocolParser.buildFrame(frameType, data);
  }

  /// 生成设备状态帧数据
  Uint8List _generateDeviceStatusFrame() {
    DeviceStatus status = generateMockDeviceStatus();
    
    List<int> data = [];
    data.add(status.rgbColor);
    data.add(status.rgbMode);
    
    // 添加压力值（float32）
    data.addAll(_float32ToBytes(status.pressureValue));
    
    // 添加电池电压（float32）
    data.addAll(_float32ToBytes(status.batteryVoltage));
    
    return Uint8List.fromList(data);
  }

  /// 生成运动数据帧数据
  Uint8List _generateExerciseDataFrame() {
    // TODO: 根据最终协议实现
    return Uint8List.fromList([0x01, 0x02, 0x03, 0x04]);
  }

  /// 将float32转换为字节数组
  List<int> _float32ToBytes(double value) {
    var buffer = Uint8List(4);
    var data = ByteData.view(buffer.buffer);
    data.setFloat32(0, value, Endian.little);
    return buffer.toList();
  }

  /// 开始模拟数据流
  void startSimulation({
    Duration interval = const Duration(seconds: 1),
    Function(DeviceStatus)? onDeviceStatus,
    Function(RealTimeExerciseData)? onExerciseData,
  }) {
    if (_isSimulating) return;
    
    _isSimulating = true;
    _simulationTimer = Timer.periodic(interval, (timer) {
      // 70%概率生成设备状态数据
      if (_random.nextDouble() < 0.7) {
        onDeviceStatus?.call(generateMockDeviceStatus());
      }
      
      // 30%概率生成运动数据
      if (_random.nextDouble() < 0.3) {
        onExerciseData?.call(generateMockExerciseData());
      }
    });
  }

  /// 停止模拟数据流
  void stopSimulation() {
    _isSimulating = false;
    _simulationTimer?.cancel();
    _simulationTimer = null;
  }

  /// 重置模拟数据
  void resetSimulation() {
    _jumpCount = 0;
    _impactCount = 0;
    _batteryVoltage = 4.0;
    _currentState = ExerciseState.standing;
  }

  /// 模拟连接延迟
  Future<bool> simulateConnection({Duration delay = const Duration(seconds: 2)}) async {
    await Future.delayed(delay);
    return _random.nextDouble() < 0.9; // 90%成功率
  }

  /// 模拟数据传输延迟
  Future<bool> simulateDataTransmission({Duration delay = const Duration(milliseconds: 100)}) async {
    await Future.delayed(delay);
    return _random.nextDouble() < 0.95; // 95%成功率
  }

  /// 验证协议解析
  bool validateProtocolParsing() {
    try {
      // 测试设备状态帧
      Uint8List statusFrame = generateMockDataFrame(DataFrameType.deviceStatus);
      ParseResult statusResult = ProtocolParser.parseFrame(statusFrame);
      
      if (!statusResult.success) {
        print('设备状态帧解析失败: ${statusResult.error}');
        return false;
      }
      
      // 测试运动数据帧
      Uint8List exerciseFrame = generateMockDataFrame(DataFrameType.exerciseData);
      ParseResult exerciseResult = ProtocolParser.parseFrame(exerciseFrame);
      
      if (!exerciseResult.success) {
        print('运动数据帧解析失败: ${exerciseResult.error}');
        return false;
      }
      
      print('协议解析测试通过');
      return true;
    } catch (e) {
      print('协议解析测试异常: $e');
      return false;
    }
  }

  /// 获取当前模拟状态
  bool get isSimulating => _isSimulating;
  
  /// 获取当前运动状态
  ExerciseState get currentState => _currentState;
  
  /// 获取当前统计数据
  Map<String, dynamic> getCurrentStats() {
    return {
      'jumpCount': _jumpCount,
      'impactCount': _impactCount,
      'batteryVoltage': _batteryVoltage,
      'currentState': _currentState.toString(),
    };
  }
} 