import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/device_info.dart';
import '../models/exercise_data.dart';
import 'bluetooth_service.dart';
import 'protocol_parser.dart';

/// 设备管理器
class DeviceManager extends ChangeNotifier {
  static final DeviceManager _instance = DeviceManager._internal();
  factory DeviceManager() => _instance;
  DeviceManager._internal() {
    _initializeStreams();
  }

  final BluetoothManager _bluetoothService = BluetoothManager();
  
  // 当前状态
  DeviceConnectionState _connectionState = DeviceConnectionState.disconnected;
  DeviceInfo? _connectedDevice;
  DeviceStatus? _currentDeviceStatus;
  RealTimeExerciseData? _currentExerciseData;
  List<DeviceInfo> _availableDevices = [];
  
  // 自动重连相关
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 3;
  static const Duration reconnectInterval = Duration(seconds: 5);
  
  // 数据缓存
  final List<RealTimeExerciseData> _exerciseDataHistory = [];
  final List<DeviceStatus> _deviceStatusHistory = [];

  // 公共访问器
  DeviceConnectionState get connectionState => _connectionState;
  DeviceInfo? get connectedDevice => _connectedDevice;
  DeviceStatus? get currentDeviceStatus => _currentDeviceStatus;
  RealTimeExerciseData? get currentExerciseData => _currentExerciseData;
  List<DeviceInfo> get availableDevices => List.unmodifiable(_availableDevices);
  List<RealTimeExerciseData> get exerciseDataHistory => List.unmodifiable(_exerciseDataHistory);
  List<DeviceStatus> get deviceStatusHistory => List.unmodifiable(_deviceStatusHistory);

  /// 初始化设备管理器
  Future<bool> initialize() async {
    try {
      bool success = await _bluetoothService.initialize();
      if (success) {
        _initializeStreams();
      }
      return success;
    } catch (e) {
      debugPrint('设备管理器初始化失败: $e');
      return false;
    }
  }

  /// 开始扫描设备
  Future<void> startDeviceScan({Duration timeout = const Duration(seconds: 10)}) async {
    try {
      await _bluetoothService.startScan(timeout: timeout);
    } catch (e) {
      debugPrint('扫描设备失败: $e');
    }
  }

  /// 停止扫描设备
  Future<void> stopDeviceScan() async {
    await _bluetoothService.stopScan();
  }

  /// 连接设备
  Future<bool> connectDevice(DeviceInfo deviceInfo) async {
    try {
      _cancelReconnectTimer();
      
      bool success = await _bluetoothService.connectDevice(deviceInfo);
      if (success) {
        _connectedDevice = deviceInfo;
        _reconnectAttempts = 0;
        
        // 连接成功后查询设备状态
        await queryDeviceStatus();
        
        // 根据设备代数启动相应的数据处理
        if (deviceInfo.generation == DeviceGeneration.generation1) {
          _startRealTimeDataCollection();
        }
        
        notifyListeners();
      }
      
      return success;
    } catch (e) {
      debugPrint('连接设备失败: $e');
      return false;
    }
  }

  /// 断开设备连接
  Future<void> disconnectDevice() async {
    try {
      _cancelReconnectTimer();
      await _bluetoothService.disconnect();
      _connectedDevice = null;
      _currentDeviceStatus = null;
      _currentExerciseData = null;
      notifyListeners();
    } catch (e) {
      debugPrint('断开设备连接失败: $e');
    }
  }

  /// 查询设备状态
  Future<bool> queryDeviceStatus() async {
    return await _bluetoothService.queryDeviceStatus();
  }

  /// 控制设备（灯效等）
  Future<bool> controlDevice({required int rgbColor, required int rgbMode}) async {
    return await _bluetoothService.controlDevice(rgbColor, rgbMode);
  }

  /// 检测设备代数
  DeviceGeneration detectDeviceGeneration(DeviceInfo deviceInfo) {
    // TODO: 根据设备信息或固件版本检测设备代数
    // 暂时返回第一代，后续可根据实际情况实现
    return DeviceGeneration.generation1;
  }

  /// 获取设备电池电量百分比
  double? getBatteryPercentage() {
    if (_currentDeviceStatus == null) return null;
    
    // 根据电池电压计算电量百分比
    double voltage = _currentDeviceStatus!.batteryVoltage;
    
    // 锂电池电压范围通常为3.0V-4.2V
    double minVoltage = 3.0;
    double maxVoltage = 4.2;
    
    if (voltage <= minVoltage) return 0.0;
    if (voltage >= maxVoltage) return 100.0;
    
    return ((voltage - minVoltage) / (maxVoltage - minVoltage)) * 100.0;
  }

  /// 获取设备信号强度描述
  String getSignalStrengthDescription(int rssi) {
    if (rssi >= -50) return '优秀';
    if (rssi >= -60) return '良好';
    if (rssi >= -70) return '一般';
    return '较差';
  }

  /// 清除历史数据
  void clearHistory() {
    _exerciseDataHistory.clear();
    _deviceStatusHistory.clear();
    notifyListeners();
  }

  /// 获取运动统计数据
  ExerciseStatistics getExerciseStatistics() {
    if (_exerciseDataHistory.isEmpty) {
      return ExerciseStatistics.empty();
    }

    int totalJumps = _exerciseDataHistory.fold(0, (sum, data) => sum + data.jumpCount);
    int totalImpacts = _exerciseDataHistory.fold(0, (sum, data) => sum + data.impactCount);
    double maxJumpHeight = _exerciseDataHistory.fold(0.0, (max, data) => 
        data.jumpHeight > max ? data.jumpHeight : max);
    double avgImpactForce = _exerciseDataHistory.fold(0.0, (sum, data) => 
        sum + data.impactForce) / _exerciseDataHistory.length;

    return ExerciseStatistics(
      totalJumps: totalJumps,
      totalImpacts: totalImpacts,
      maxJumpHeight: maxJumpHeight,
      avgImpactForce: avgImpactForce,
      dataPoints: _exerciseDataHistory.length,
    );
  }

  /// 初始化数据流监听
  void _initializeStreams() {
    // 监听设备扫描结果
    _bluetoothService.devicesStream.listen((devices) {
      _availableDevices = devices;
      notifyListeners();
    });

    // 监听连接状态变化
    _bluetoothService.connectionStateStream.listen((state) {
      _connectionState = state;
      
      if (state == DeviceConnectionState.disconnected && _connectedDevice != null) {
        _handleUnexpectedDisconnection();
      }
      
      notifyListeners();
    });

    // 监听设备状态数据
    _bluetoothService.deviceStatusStream.listen((status) {
      _currentDeviceStatus = status;
      _deviceStatusHistory.add(status);
      
      // 保持历史记录数量限制
      if (_deviceStatusHistory.length > 1000) {
        _deviceStatusHistory.removeRange(0, _deviceStatusHistory.length - 1000);
      }
      
      notifyListeners();
    });

    // 监听运动数据
    _bluetoothService.exerciseDataStream.listen((data) {
      _currentExerciseData = data;
      _exerciseDataHistory.add(data);
      
      // 保持历史记录数量限制
      if (_exerciseDataHistory.length > 1000) {
        _exerciseDataHistory.removeRange(0, _exerciseDataHistory.length - 1000);
      }
      
      notifyListeners();
    });
  }

  /// 处理意外断开连接
  void _handleUnexpectedDisconnection() {
    if (_connectedDevice != null && _reconnectAttempts < maxReconnectAttempts) {
      _startReconnectTimer();
    }
  }

  /// 启动重连定时器
  void _startReconnectTimer() {
    _cancelReconnectTimer();
    
    _reconnectTimer = Timer(reconnectInterval, () async {
      if (_connectedDevice != null) {
        debugPrint('尝试重新连接设备... (第${_reconnectAttempts + 1}次)');
        
        bool success = await connectDevice(_connectedDevice!);
        if (!success) {
          _reconnectAttempts++;
          if (_reconnectAttempts < maxReconnectAttempts) {
            _startReconnectTimer();
          } else {
            debugPrint('重连失败，已达到最大重试次数');
            _connectedDevice = null;
            notifyListeners();
          }
        }
      }
    });
  }

  /// 取消重连定时器
  void _cancelReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// 启动实时数据收集（第一代设备）
  void _startRealTimeDataCollection() {
    // 定期查询设备状态
    Timer.periodic(const Duration(seconds: 2), (timer) {
      if (_connectionState != DeviceConnectionState.connected) {
        timer.cancel();
        return;
      }
      queryDeviceStatus();
    });
  }

  @override
  void dispose() {
    _cancelReconnectTimer();
    _bluetoothService.dispose();
    super.dispose();
  }
}

/// 运动统计数据
class ExerciseStatistics {
  final int totalJumps;
  final int totalImpacts;
  final double maxJumpHeight;
  final double avgImpactForce;
  final int dataPoints;

  const ExerciseStatistics({
    required this.totalJumps,
    required this.totalImpacts,
    required this.maxJumpHeight,
    required this.avgImpactForce,
    required this.dataPoints,
  });

  factory ExerciseStatistics.empty() {
    return const ExerciseStatistics(
      totalJumps: 0,
      totalImpacts: 0,
      maxJumpHeight: 0.0,
      avgImpactForce: 0.0,
      dataPoints: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalJumps': totalJumps,
      'totalImpacts': totalImpacts,
      'maxJumpHeight': maxJumpHeight,
      'avgImpactForce': avgImpactForce,
      'dataPoints': dataPoints,
    };
  }
} 