import 'dart:typed_data';
import 'dart:convert';
import '../models/exercise_data.dart';
import '../models/device_info.dart';

/// 数据协议解析器
class ProtocolParser {
  // 协议常量
  static const int frameHeader = 0xA5;
  static const int frameFooter = 0x5A;
  static const int minFrameLength = 6; // 帧头 + 帧ID + 长度 + 校验和(2) + 帧尾

  /// 解析数据帧
  static ParseResult parseFrame(Uint8List data) {
    try {
      if (data.length < minFrameLength) {
        return ParseResult.error('数据帧长度不足');
      }

      // 验证帧头
      if (data[0] != frameHeader) {
        return ParseResult.error('帧头错误');
      }

      // 获取帧ID和数据长度
      int frameId = data[1];
      int dataLength = data[2];

      // 验证总长度
      int expectedLength = 3 + dataLength + 2 + 1; // 帧头+帧ID+长度+数据+校验和+帧尾
      if (data.length < expectedLength) {
        return ParseResult.error('数据帧长度不匹配');
      }

      // 验证帧尾
      if (data[expectedLength - 1] != frameFooter) {
        return ParseResult.error('帧尾错误');
      }

      // 提取数据段
      Uint8List dataSegment = data.sublist(3, 3 + dataLength);
      
      // 提取校验和
      int receivedChecksum = (data[3 + dataLength + 1] << 8) | data[3 + dataLength];
      
      // 验证校验和
      int calculatedChecksum = calculateChecksum(dataSegment);
      if (receivedChecksum != calculatedChecksum) {
        return ParseResult.error('校验和错误');
      }

      // 根据帧ID解析具体数据
      DataFrameType frameType = DataFrameType.fromId(frameId);
      dynamic parsedData = _parseDataByType(frameType, dataSegment);

      return ParseResult.success(frameType, parsedData);
    } catch (e) {
      return ParseResult.error('解析异常: $e');
    }
  }

  /// 构造数据帧
  static Uint8List buildFrame(DataFrameType frameType, Uint8List data) {
    // 计算校验和
    int checksum = calculateChecksum(data);
    
    // 构造完整帧
    List<int> frame = [
      frameHeader,           // 帧头
      frameType.id,          // 帧ID
      data.length,           // 数据长度
      ...data,               // 数据段
      checksum & 0xFF,       // 校验和低字节
      (checksum >> 8) & 0xFF, // 校验和高字节
      frameFooter,           // 帧尾
    ];

    return Uint8List.fromList(frame);
  }

  /// 计算校验和（数据段的低16位和校验）
  static int calculateChecksum(Uint8List data) {
    int sum = 0;
    for (int byte in data) {
      sum += byte;
    }
    return sum & 0xFFFF; // 取低16位
  }

  /// 根据帧类型解析数据
  static dynamic _parseDataByType(DataFrameType frameType, Uint8List data) {
    switch (frameType) {
      case DataFrameType.deviceStatus:
        return _parseDeviceStatus(data);
      case DataFrameType.exerciseData:
        return _parseExerciseData(data);
      default:
        return data; // 返回原始数据
    }
  }

  /// 解析设备状态数据
  static DeviceStatus _parseDeviceStatus(Uint8List data) {
    if (data.length < 10) {
      throw Exception('设备状态数据长度不足');
    }

    // 解析数据字段
    int rgbColor = data[0];
    int rgbMode = data[1];
    
    // 解析float32压力值 (字节2-5)
    double pressureValue = _parseFloat32(data.sublist(2, 6));
    
    // 解析float32电池电压 (字节6-9)
    double batteryVoltage = _parseFloat32(data.sublist(6, 10));

    return DeviceStatus(
      rgbColor: rgbColor,
      rgbMode: rgbMode,
      pressureValue: pressureValue,
      batteryVoltage: batteryVoltage,
      timestamp: DateTime.now(),
    );
  }

  /// 解析运动数据（待完善）
  static RealTimeExerciseData _parseExerciseData(Uint8List data) {
    // TODO: 根据最终协议实现
    return RealTimeExerciseData(
      state: ExerciseState.standing,
      impactForce: 0.0,
      dampingForce: 0.0,
      impactCount: 0,
      jumpCount: 0,
      jumpHeight: 0.0,
      timestamp: DateTime.now(),
    );
  }

  /// 解析IEEE-754 float32格式
  static double _parseFloat32(Uint8List bytes) {
    if (bytes.length != 4) {
      throw Exception('Float32数据长度必须为4字节');
    }
    
    ByteData byteData = ByteData.sublistView(bytes);
    return byteData.getFloat32(0, Endian.little);
  }

  /// 将float32转换为字节数组
  static Uint8List _float32ToBytes(double value) {
    ByteData byteData = ByteData(4);
    byteData.setFloat32(0, value, Endian.little);
    return byteData.buffer.asUint8List();
  }

  /// 解析GPS数据（动态长度）
  static List<GPSData> parseGPSData(Uint8List data) {
    List<GPSData> gpsDataList = [];
    int offset = 0;

    while (offset < data.length) {
      if (offset + 1 >= data.length) break;
      
      int gpsLength = data[offset];
      offset++;
      
      if (offset + gpsLength > data.length) break;
      
      Uint8List gpsBytes = data.sublist(offset, offset + gpsLength);
      // TODO: 根据具体GPS数据格式解析
      // 这里需要根据实际GPS数据格式实现
      
      offset += gpsLength;
    }

    return gpsDataList;
  }

  /// 验证设备广播包
  static bool isValidDevice(List<int> advertisementData) {
    // 验证广播包数据: 0x0201060E09585441AE442D416972482D473105FFF7040203
    List<int> expectedData = [
      0x02, 0x01, 0x06, 0x0E, 0x09, 0x58, 0x54, 0x41, 0xAE, 0x44, 0x2D, 0x41,
      0x69, 0x72, 0x48, 0x2D, 0x47, 0x31, 0x05, 0xFF, 0xF7, 0x04, 0x02, 0x03
    ];

    if (advertisementData.length < expectedData.length) {
      return false;
    }

    // 检查制造商ID (0x04F7)
    for (int i = 0; i < advertisementData.length - 1; i++) {
      if (advertisementData[i] == 0xF7 && advertisementData[i + 1] == 0x04) {
        return true;
      }
    }

    return false;
  }
}

/// 解析结果类
class ParseResult {
  final bool success;
  final DataFrameType? frameType;
  final dynamic data;
  final String? error;

  ParseResult.success(this.frameType, this.data) 
      : success = true, error = null;

  ParseResult.error(this.error) 
      : success = false, frameType = null, data = null;

  @override
  String toString() {
    if (success) {
      return 'ParseResult.success(frameType: $frameType, data: $data)';
    } else {
      return 'ParseResult.error(error: $error)';
    }
  }
}

/// 数据帧构造器
class FrameBuilder {
  /// 构造设备状态查询帧
  static Uint8List buildStatusQueryFrame() {
    return ProtocolParser.buildFrame(
      DataFrameType.deviceStatus,
      Uint8List(0), // 空数据
    );
  }

  /// 构造设备控制帧
  static Uint8List buildControlFrame(int rgbColor, int rgbMode) {
    List<int> controlData = [rgbColor, rgbMode];
    return ProtocolParser.buildFrame(
      DataFrameType.deviceStatus,
      Uint8List.fromList(controlData),
    );
  }
} 