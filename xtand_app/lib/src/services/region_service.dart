import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:http/http.dart' as http;

class RegionService {
  static RegionService? _instance;
  static RegionService get instance => _instance ??= RegionService._();
  
  RegionService._();

  bool? _isInChina;
  
  /// 检测用户是否在中国 - 综合多种方法
  /// 返回 true 表示在中国，false 表示在海外
  Future<bool> isInChina() async {
    if (_isInChina != null) {
      return _isInChina!;
    }

    try {
      int chinaScore = 0;
      int totalChecks = 0;

      // 方法1: 系统语言和地区设置 (权重: 30%)
      totalChecks++;
      final locale = WidgetsBinding.instance.platformDispatcher.locale;
      if (locale.countryCode == 'CN') {
        chinaScore += 30;
        debugPrint('地区检测: 系统语言显示中国 (+30分)');
      }

      // 方法2: 时区检测 (权重: 20%)
      totalChecks++;
      final timeZone = DateTime.now().timeZoneName;
      if (timeZone.contains('China') ||
          timeZone.contains('Asia/Shanghai') ||
          timeZone.contains('Asia/Beijing')) {
        chinaScore += 20;
        debugPrint('地区检测: 时区显示中国 (+20分)');
      }

      // 方法3: IP地址检测 (权重: 40%) - 最重要
      totalChecks++;
      final ipResult = await _detectByIP();
      if (ipResult) {
        chinaScore += 40;
        debugPrint('地区检测: IP地址显示中国 (+40分)');
      }

      // 方法4: 网络连通性测试 (权重: 10%)
      totalChecks++;
      final connectivityResult = await _detectByConnectivity();
      if (connectivityResult) {
        chinaScore += 10;
        debugPrint('地区检测: 网络环境显示中国 (+10分)');
      }

      debugPrint('地区检测总分: $chinaScore/100');

      // 如果得分超过50分，判断为中国
      _isInChina = chinaScore >= 50;
      return _isInChina!;

    } catch (e) {
      debugPrint('地区检测失败: $e');
      // 出错时使用系统语言作为备选方案
      final locale = WidgetsBinding.instance.platformDispatcher.locale;
      _isInChina = locale.countryCode == 'CN';
      return _isInChina!;
    }
  }

  /// IP地址地理位置检测
  Future<bool> _detectByIP() async {
    try {
      // 使用免费的IP地理位置API
      final response = await http.get(
        Uri.parse('https://ipapi.co/country_code/'),
        headers: {'User-Agent': 'XtandApp/1.0'},
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final countryCode = response.body.trim();
        return countryCode == 'CN';
      }
    } catch (e) {
      debugPrint('IP检测失败: $e');
    }

    // 备选API
    try {
      final response = await http.get(
        Uri.parse('http://ip-api.com/json/?fields=countryCode'),
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['countryCode'] == 'CN';
      }
    } catch (e) {
      debugPrint('备选IP检测失败: $e');
    }

    return false;
  }

  /// 网络连通性检测
  Future<bool> _detectByConnectivity() async {
    try {
      // 测试访问百度 (中国可访问)
      final baiduResponse = await http.head(
        Uri.parse('https://www.baidu.com'),
      ).timeout(const Duration(seconds: 3));

      if (baiduResponse.statusCode == 200) {
        // 再测试Google (中国通常不可访问)
        try {
          final googleResponse = await http.head(
            Uri.parse('https://www.google.com'),
          ).timeout(const Duration(seconds: 2));

          // 如果Google也能访问，可能不在中国
          if (googleResponse.statusCode == 200) {
            return false;
          }
        } catch (e) {
          // Google访问失败，可能在中国
          return true;
        }
      }
    } catch (e) {
      debugPrint('网络连通性检测失败: $e');
    }

    return false;
  }

  /// 获取支持的登录方式
  Future<List<LoginMethod>> getSupportedLoginMethods() async {
    final inChina = await isInChina();
    
    if (inChina) {
      // 中国地区支持所有登录方式
      return [
        LoginMethod.phone,
        LoginMethod.email,
        LoginMethod.wechat,
        LoginMethod.apple,
      ];
    } else {
      // 海外地区只支持邮箱和Apple ID
      return [
        LoginMethod.email,
        LoginMethod.apple,
      ];
    }
  }

  /// 获取默认登录方式
  Future<LoginMethod> getDefaultLoginMethod() async {
    final inChina = await isInChina();
    return inChina ? LoginMethod.phone : LoginMethod.email;
  }

  /// 重置缓存（用于测试或用户手动切换地区）
  void resetCache() {
    _isInChina = null;
  }

  /// 手动设置地区（用于测试或用户手动选择）
  void setRegion({required bool isInChina}) {
    _isInChina = isInChina;
  }
}

enum LoginMethod { phone, email, wechat, apple }

extension LoginMethodExtension on LoginMethod {
  String get displayName {
    switch (this) {
      case LoginMethod.phone:
        return '手机号';
      case LoginMethod.email:
        return '邮箱';
      case LoginMethod.wechat:
        return '微信';
      case LoginMethod.apple:
        return 'Apple ID';
    }
  }

  bool get isAvailableInChina {
    return true; // 所有方式在中国都可用
  }

  bool get isAvailableOverseas {
    switch (this) {
      case LoginMethod.phone:
      case LoginMethod.wechat:
        return false; // 手机号和微信在海外不可用
      case LoginMethod.email:
      case LoginMethod.apple:
        return true; // 邮箱和Apple ID在海外可用
    }
  }
}
