import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

class RegionService {
  static RegionService? _instance;
  static RegionService get instance => _instance ??= RegionService._();
  
  RegionService._();

  bool? _isInChina;
  
  /// 检测用户是否在中国
  /// 返回 true 表示在中国，false 表示在海外
  Future<bool> isInChina() async {
    if (_isInChina != null) {
      return _isInChina!;
    }

    try {
      // 方法1: 检查系统语言和地区设置
      final locale = WidgetsBinding.instance.platformDispatcher.locale;
      if (locale.countryCode == 'CN') {
        _isInChina = true;
        return true;
      }

      // 方法2: 检查时区
      final timeZone = DateTime.now().timeZoneName;
      if (timeZone.contains('China') || timeZone.contains('CST')) {
        _isInChina = true;
        return true;
      }

      // 方法3: 在实际应用中，可以通过IP地址检测
      // 这里可以调用IP地理位置API
      // final ipLocation = await _getLocationByIP();
      // if (ipLocation.country == 'CN') {
      //   _isInChina = true;
      //   return true;
      // }

      // 默认为海外
      _isInChina = false;
      return false;
    } catch (e) {
      debugPrint('地区检测失败: $e');
      // 出错时默认为海外，确保应用可用性
      _isInChina = false;
      return false;
    }
  }

  /// 获取支持的登录方式
  Future<List<LoginMethod>> getSupportedLoginMethods() async {
    final inChina = await isInChina();
    
    if (inChina) {
      // 中国地区支持所有登录方式
      return [
        LoginMethod.phone,
        LoginMethod.email,
        LoginMethod.wechat,
        LoginMethod.apple,
      ];
    } else {
      // 海外地区只支持邮箱和Apple ID
      return [
        LoginMethod.email,
        LoginMethod.apple,
      ];
    }
  }

  /// 获取默认登录方式
  Future<LoginMethod> getDefaultLoginMethod() async {
    final inChina = await isInChina();
    return inChina ? LoginMethod.phone : LoginMethod.email;
  }

  /// 重置缓存（用于测试或用户手动切换地区）
  void resetCache() {
    _isInChina = null;
  }

  /// 手动设置地区（用于测试或用户手动选择）
  void setRegion({required bool isInChina}) {
    _isInChina = isInChina;
  }
}

enum LoginMethod { phone, email, wechat, apple }

extension LoginMethodExtension on LoginMethod {
  String get displayName {
    switch (this) {
      case LoginMethod.phone:
        return '手机号';
      case LoginMethod.email:
        return '邮箱';
      case LoginMethod.wechat:
        return '微信';
      case LoginMethod.apple:
        return 'Apple ID';
    }
  }

  bool get isAvailableInChina {
    return true; // 所有方式在中国都可用
  }

  bool get isAvailableOverseas {
    switch (this) {
      case LoginMethod.phone:
      case LoginMethod.wechat:
        return false; // 手机号和微信在海外不可用
      case LoginMethod.email:
      case LoginMethod.apple:
        return true; // 邮箱和Apple ID在海外可用
    }
  }
}
