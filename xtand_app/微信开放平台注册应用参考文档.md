# 微信开放平台注册应用参考文档

## 1. 前置准备

- 微信开放平台账号（https://open.weixin.qq.com/）
- 企业/个人开发者资质
- 应用图标、简介等基本资料

---

## 2. 关键信息

### Android 应用

- **包名（Package Name）**：`com.mebotx.xtand`
- **应用签名（SHA1）**：`e94f82ddd0772b54ee73ce8dbd1c95b8a3ad44dd`

### iOS 应用

- **Bundle ID**：`com.mebotx.xtand`

---

## 3. 注册流程

```mermaid
flowchart TD
    A[准备信息] --> B[登录微信开放平台]
    B --> C[创建应用]
    C --> D[填写应用信息]
    D --> E[配置Android信息]
    D --> F[配置iOS信息]
    E --> G[填写包名和签名]
    F --> H[填写Bundle ID]
    G --> I[提交审核]
    H --> I
    I --> J[等待审核通过]
    J --> K[集成SDK并开发]
```

---

## 4. 详细步骤

### 4.1 登录微信开放平台

- 访问 [微信开放平台](https://open.weixin.qq.com/)
- 使用开发者账号登录

### 4.2 创建移动应用

- 进入“管理中心” → “移动应用” → “创建应用”
- 填写应用名称、简介、图标等基本信息

### 4.3 配置 Android 信息

- 在“应用信息”页面，选择“Android平台”
- 填写包名：`com.mebotx.xtand`
- 填写应用签名（SHA1）：`e94f82ddd0772b54ee73ce8dbd1c95b8a3ad44dd`
- 按照页面指引上传 APK 或填写下载地址

### 4.4 配置 iOS 信息

- 在“应用信息”页面，选择“iOS平台”
- 填写 Bundle ID：`com.mebotx.xtand`

### 4.5 提交审核

- 检查所有信息无误后，提交应用审核
- 等待微信官方审核通过

---

## 5. 审核通过后

- 获取 AppID、AppSecret
- 集成微信 SDK（如微信登录、分享等功能）
- 按照微信开放平台文档进行开发和测试

---

## 6. 常见问题

- **包名/Bundle ID 必须与实际应用一致，否则无法通过审核**
- **Android 签名必须为正式签名（release），调试签名无效**
- **如需修改包名/Bundle ID，需重新提交审核**
